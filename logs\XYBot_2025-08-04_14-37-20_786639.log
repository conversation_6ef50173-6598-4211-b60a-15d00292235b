2025-08-04 14:37:24 | SUCCESS | 读取主设置成功
2025-08-04 14:37:24 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-04 14:37:24 | INFO | 2025/08/04 14:37:24 GetRedisAddr: 127.0.0.1:6379
2025-08-04 14:37:24 | INFO | 2025/08/04 14:37:24 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-04 14:37:24 | INFO | 2025/08/04 14:37:24 Server start at :9000
2025-08-04 14:37:24 | SUCCESS | WechatAPI服务已启动
2025-08-04 14:37:25 | SUCCESS | 获取到登录uuid: oeYFDrvBtj05SEOFD7dv
2025-08-04 14:37:25 | INFO | 等待登录中，过期倒计时：239
2025-08-04 14:37:32 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-04 14:37:32 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-04 14:37:32 | SUCCESS | 登录成功
2025-08-04 14:37:32 | SUCCESS | 已开启自动心跳
2025-08-04 14:37:32 | INFO | 成功加载表情映射文件，共 550 条记录
2025-08-04 14:37:32 | SUCCESS | 数据库初始化成功
2025-08-04 14:37:32 | SUCCESS | 定时任务已启动
2025-08-04 14:37:32 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-04 14:37:32 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-04 14:37:34 | INFO | 播客API初始化成功
2025-08-04 14:37:34 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['***********@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['***********@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['***********@chatroom', '***********@chatroom']}}
2025-08-04 14:37:34 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['***********@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['***********@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['***********@chatroom', '***********@chatroom']}}
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-04 14:37:34 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-04 14:37:34 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-04 14:37:34 | INFO | [ChatSummary] 数据库初始化成功
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-04 14:37:35 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-04 14:37:35 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-04 14:37:35 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-04 14:37:35 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-04 14:37:35 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-04 14:37:35 | DEBUG |   - 启用状态: True
2025-08-04 14:37:35 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-04 14:37:35 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-04 14:37:35 | DEBUG |   - Web ID: 7532989324985157172
2025-08-04 14:37:35 | DEBUG |   - Cookies配置: 已配置
2025-08-04 14:37:35 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-04 14:37:35 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-04 14:37:35 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-04 14:37:35 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-04 14:37:35 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-04 14:37:35 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-04 14:37:35 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-04 14:37:35 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-04 14:37:35 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-04 14:37:35 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-04 14:37:35 | INFO | [RenameReminder] 开始启用插件...
2025-08-04 14:37:35 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-04 14:37:35 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-04 14:37:35 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-04 14:37:35 | INFO | 已设置检查间隔为 3600 秒
2025-08-04 14:37:35 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-04 14:37:36 | DEBUG | 已更新群 ***********@chatroom 的成员列表
2025-08-04 14:37:36 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-04 14:37:41 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-04 14:37:41 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-04 14:37:43 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-04 14:37:43 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-04 14:37:43 | INFO | [yuanbao] 插件初始化完成
2025-08-04 14:37:43 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-04 14:37:43 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-04 14:37:43 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-04 14:37:43 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-04 14:37:43 | INFO | 处理堆积消息中
2025-08-04 14:37:45 | DEBUG | 接受到 26 条消息
2025-08-04 14:37:47 | SUCCESS | 处理堆积消息完毕
2025-08-04 14:37:47 | SUCCESS | 开始处理消息
2025-08-04 14:37:48 | DEBUG | 收到消息: {'MsgId': 548765395, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n让她歇歇会'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289474, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eZDfQwiW|v1_k0yRnL4r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3090264469906007258, 'MsgSeq': 871424842}
2025-08-04 14:37:48 | INFO | 收到文本消息: 消息ID:548765395 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:让她歇歇会
2025-08-04 14:37:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '让她歇歇会' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-04 14:37:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['让她歇歇会']
2025-08-04 14:37:48 | INFO | 成功加载表情映射文件，共 550 条记录
2025-08-04 14:37:48 | DEBUG | 处理消息内容: '让她歇歇会'
2025-08-04 14:37:48 | DEBUG | 消息内容 '让她歇歇会' 不匹配任何命令，忽略
2025-08-04 14:37:50 | DEBUG | 收到消息: {'MsgId': 1641455910, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n哦，才发现'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289476, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_5ZM3KFfk|v1_LkVnkunW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1145200174330110109, 'MsgSeq': 871424843}
2025-08-04 14:37:50 | INFO | 收到文本消息: 消息ID:1641455910 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:哦，才发现
2025-08-04 14:37:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哦，才发现' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:37:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['哦，才发现']
2025-08-04 14:37:50 | DEBUG | 处理消息内容: '哦，才发现'
2025-08-04 14:37:50 | DEBUG | 消息内容 '哦，才发现' 不匹配任何命令，忽略
2025-08-04 14:37:54 | DEBUG | 收到消息: {'MsgId': 1242633790, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n最近进出频繁'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289481, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_enRh8vm5|v1_ndzT4uOp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3043771327361019941, 'MsgSeq': 871424844}
2025-08-04 14:37:54 | INFO | 收到文本消息: 消息ID:1242633790 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:最近进出频繁
2025-08-04 14:37:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '最近进出频繁' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-04 14:37:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['最近进出频繁']
2025-08-04 14:37:54 | DEBUG | 处理消息内容: '最近进出频繁'
2025-08-04 14:37:54 | DEBUG | 消息内容 '最近进出频繁' 不匹配任何命令，忽略
2025-08-04 14:37:57 | DEBUG | 收到消息: {'MsgId': 234447407, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n哈哈哈 来不及欢迎'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289484, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Xkt5A7Qf|v1_DehZkC/o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9110870120062462761, 'MsgSeq': 871424845}
2025-08-04 14:37:57 | INFO | 收到文本消息: 消息ID:234447407 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:哈哈哈 来不及欢迎
2025-08-04 14:37:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈 来不及欢迎' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-04 14:37:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈', '来不及欢迎']
2025-08-04 14:37:57 | DEBUG | 处理消息内容: '哈哈哈 来不及欢迎'
2025-08-04 14:37:57 | DEBUG | 消息内容 '哈哈哈 来不及欢迎' 不匹配任何命令，忽略
2025-08-04 14:38:05 | DEBUG | 收到消息: {'MsgId': 325471530, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n上线了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289492, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_FXkAAaIT|v1_aTjOZsxO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 958776989552315232, 'MsgSeq': 871424846}
2025-08-04 14:38:05 | INFO | 收到文本消息: 消息ID:325471530 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:上线了
2025-08-04 14:38:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '上线了' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:38:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['上线了']
2025-08-04 14:38:05 | DEBUG | 处理消息内容: '上线了'
2025-08-04 14:38:05 | DEBUG | 消息内容 '上线了' 不匹配任何命令，忽略
2025-08-04 14:38:21 | DEBUG | 收到消息: {'MsgId': 2092323049, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n欢迎大佬来消费'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289508, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_jnbV7Nms|v1_7Y3rB4dP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5517896096311739536, 'MsgSeq': 871424847}
2025-08-04 14:38:21 | INFO | 收到文本消息: 消息ID:2092323049 来自:***********@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:欢迎大佬来消费
2025-08-04 14:38:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '欢迎大佬来消费' from wxid_bmzp9achod6922 in ***********@chatroom
2025-08-04 14:38:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['欢迎大佬来消费']
2025-08-04 14:38:21 | DEBUG | 处理消息内容: '欢迎大佬来消费'
2025-08-04 14:38:21 | DEBUG | 消息内容 '欢迎大佬来消费' 不匹配任何命令，忽略
2025-08-04 14:38:31 | DEBUG | 收到消息: {'MsgId': 391153503, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_558jw1jndlum22:\n@我是男人\u2005你多多消费点'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289518, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_bmzp9achod6922]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_dYeSSY82|v1_nH4IrpQC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4305206429780791334, 'MsgSeq': 871424848}
2025-08-04 14:38:31 | INFO | 收到文本消息: 消息ID:391153503 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 @:['wxid_bmzp9achod6922'] 内容:@我是男人 你多多消费点
2025-08-04 14:38:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@我是男人 你多多消费点' from wxid_558jw1jndlum22 in ***********@chatroom
2025-08-04 14:38:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['@我是男人\u2005你多多消费点']
2025-08-04 14:38:31 | DEBUG | 处理消息内容: '@我是男人 你多多消费点'
2025-08-04 14:38:31 | DEBUG | 消息内容 '@我是男人 你多多消费点' 不匹配任何命令，忽略
2025-08-04 14:38:38 | DEBUG | 收到消息: {'MsgId': 1754287202, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="functionmsg">\n<functionmsg>\n<cgi>/cgi-bin/micromsg-bin/pullfunctionmsg</cgi>\n<cmdid>614</cmdid>\n<functionmsgid>MMKANYIKAN_REDDOT_1754287500</functionmsgid>\n<retryinterval>150</retryinterval>\n<retrycount>3</retrycount>\n<custombuff>YImkAXIKMTc1NDI4NzUwMHoGd2VpeGluigEcTU1LQU5ZSUtBTl9SRURET1RfMTc1NDI4NzUwMKoBWFhqSnNUNjQ2QytMeExHckVPSndXbE0zdmxldlBmL2I2NndYRW03V2hBcmdDdE1FMFZjd1lnQ2NOQ1JQazdvcFVxL0s4U3F5a0NWcFg2U3hSRjY4cER3PT0=</custombuff>\n<businessid>21001</businessid>\n<actiontime>1754287202</actiontime>\n<functionmsgversion>2</functionmsgversion>\n</functionmsg>\n</sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289525, 'NewMsgId': 1754287202, 'MsgSeq': 0}
2025-08-04 14:38:38 | DEBUG | 系统消息类型: functionmsg
2025-08-04 14:38:38 | INFO | 未知的系统消息类型: {'MsgId': 1754287202, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="functionmsg">\n<functionmsg>\n<cgi>/cgi-bin/micromsg-bin/pullfunctionmsg</cgi>\n<cmdid>614</cmdid>\n<functionmsgid>MMKANYIKAN_REDDOT_1754287500</functionmsgid>\n<retryinterval>150</retryinterval>\n<retrycount>3</retrycount>\n<custombuff>YImkAXIKMTc1NDI4NzUwMHoGd2VpeGluigEcTU1LQU5ZSUtBTl9SRURET1RfMTc1NDI4NzUwMKoBWFhqSnNUNjQ2QytMeExHckVPSndXbE0zdmxldlBmL2I2NndYRW03V2hBcmdDdE1FMFZjd1lnQ2NOQ1JQazdvcFVxL0s4U3F5a0NWcFg2U3hSRjY4cER3PT0=</custombuff>\n<businessid>21001</businessid>\n<actiontime>1754287202</actiontime>\n<functionmsgversion>2</functionmsgversion>\n</functionmsg>\n</sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289525, 'NewMsgId': 1754287202, 'MsgSeq': 0, 'FromWxid': 'weixin', 'SenderWxid': 'weixin', 'IsGroup': False}
2025-08-04 14:38:47 | DEBUG | 收到消息: {'MsgId': 1871708417, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n@ও゛风゛\u2005欢迎进R团消费的老板'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289535, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_afev4f9cbcoa22</atuserlist>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_HRYHlaSu|v1_NcqMo6MD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7609354148578228222, 'MsgSeq': 871424849}
2025-08-04 14:38:47 | INFO | 收到文本消息: 消息ID:1871708417 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:['wxid_afev4f9cbcoa22'] 内容:@ও゛风゛ 欢迎进R团消费的老板
2025-08-04 14:38:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ও゛风゛ 欢迎进R团消费的老板' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-04 14:38:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['@ও゛风゛\u2005欢迎进R团消费的老板']
2025-08-04 14:38:47 | DEBUG | 处理消息内容: '@ও゛风゛ 欢迎进R团消费的老板'
2025-08-04 14:38:47 | DEBUG | 消息内容 '@ও゛风゛ 欢迎进R团消费的老板' 不匹配任何命令，忽略
2025-08-04 14:38:50 | DEBUG | 收到消息: {'MsgId': 1226323569, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n@夏颜。\u2005你赶紧充钱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289535, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_558jw1jndlum22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_3K/LWQjJ|v1_gqcNWmXX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4097646106973770166, 'MsgSeq': 871424850}
2025-08-04 14:38:50 | INFO | 收到文本消息: 消息ID:1226323569 来自:***********@chatroom 发送人:wxid_bmzp9achod6922 @:['wxid_558jw1jndlum22'] 内容:@夏颜。 你赶紧充钱
2025-08-04 14:38:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@夏颜。 你赶紧充钱' from wxid_bmzp9achod6922 in ***********@chatroom
2025-08-04 14:38:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['@夏颜。\u2005你赶紧充钱']
2025-08-04 14:38:50 | DEBUG | 处理消息内容: '@夏颜。 你赶紧充钱'
2025-08-04 14:38:50 | DEBUG | 消息内容 '@夏颜。 你赶紧充钱' 不匹配任何命令，忽略
2025-08-04 14:39:07 | DEBUG | 收到消息: {'MsgId': 1352410390, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg>\n\t<appmsg appid="" sdkver="">\n\t\t<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>\n\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>\n\t\t<lowurl><![CDATA[]]></lowurl>\n\t\t<type><![CDATA[2001]]></type>\n\t\t<title><![CDATA[微信红包]]></title>\n\t\t<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>\n\t\t<wcpayinfo>\n\t\t\t<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>\n\t\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>\n\t\t\t<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>\n\t\t\t<receivertitle><![CDATA[欢迎 风]]></receivertitle>\n\t\t\t<sendertitle><![CDATA[欢迎 风]]></sendertitle>\n\t\t\t<scenetext><![CDATA[微信红包]]></scenetext>\n\t\t\t<senderdes><![CDATA[查看红包]]></senderdes>\n\t\t\t<receiverdes><![CDATA[领取红包]]></receiverdes>\n\t\t\t<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047247267986009&sendusername=wxid_ikxxrwasicud11&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d&total_num=10]]></nativeurl>\n\t\t\t<sceneid><![CDATA[1002]]></sceneid>\n\t\t\t<innertype><![CDATA[0]]></innertype>\n\t\t\t<paymsgid><![CDATA[1000039901202508047247267986009]]></paymsgid>\n\t\t\t<scenetext>微信红包</scenetext>\n\t\t\t<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>\n\t\t\t<invalidtime><![CDATA[1754375954]]></invalidtime>\n\t\t\t<broaden />\n\t\t</wcpayinfo>\n\t</appmsg>\n\t<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289554, 'MsgSource': '<msgsource>\n\t<pushkey />\n\t<ModifyMsgAction />\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_rHGjmM/6|v1_0mx5FSg7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2647311890542542192, 'MsgSeq': 871424851}
2025-08-04 14:39:07 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-08-04 14:39:07 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[欢迎 风]]></receivertitle>
			<sendertitle><![CDATA[欢迎 风]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047247267986009&sendusername=wxid_ikxxrwasicud11&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202508047247267986009]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1754375954]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-08-04 14:39:07 | DEBUG | XML消息类型: 2001
2025-08-04 14:39:07 | DEBUG | XML消息标题: 微信红包
2025-08-04 14:39:07 | DEBUG | XML消息描述: 我给你发了一个红包，赶紧去拆!
2025-08-04 14:39:07 | DEBUG | XML消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d
2025-08-04 14:39:07 | DEBUG | XML消息缩略图URL: https://wx.gtimg.com/hongbao/1800/hb.png
2025-08-04 14:39:07 | INFO | 未知的XML消息类型: 2001
2025-08-04 14:39:07 | INFO | 消息标题: 微信红包
2025-08-04 14:39:07 | INFO | 消息描述: 我给你发了一个红包，赶紧去拆!
2025-08-04 14:39:07 | INFO | 消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d
2025-08-04 14:39:07 | INFO | 完整XML内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047247267986009&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[欢迎 风]]></receivertitle>
			<sendertitle><![CDATA[欢迎 风]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047247267986009&sendusername=wxid_ikxxrwasicud11&ver=6&sign=9588693c85e785637b84774c057e81587b0edffa8b709f20e254f73b5f04421358400bfc44a82fe2fd7c80a885d5220de885b03a6da4d2c0bc2d8376000410779d4417896523820e61d5cb0387cb154d&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202508047247267986009]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1754375954]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-08-04 14:39:09 | DEBUG | 收到消息: {'MsgId': 1982679530, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_558jw1jndlum22:\n@我是男人\u2005你给我点，我去充'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289556, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_bmzp9achod6922]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_U1gNlDsD|v1_KVsaIkOl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2587702960437277276, 'MsgSeq': 871424852}
2025-08-04 14:39:09 | INFO | 收到文本消息: 消息ID:1982679530 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 @:['wxid_bmzp9achod6922'] 内容:@我是男人 你给我点，我去充
2025-08-04 14:39:09 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@我是男人 你给我点，我去充' from wxid_558jw1jndlum22 in ***********@chatroom
2025-08-04 14:39:09 | DEBUG | [DouBaoImageToImage] 命令解析: ['@我是男人\u2005你给我点，我去充']
2025-08-04 14:39:09 | DEBUG | 处理消息内容: '@我是男人 你给我点，我去充'
2025-08-04 14:39:09 | DEBUG | 消息内容 '@我是男人 你给我点，我去充' 不匹配任何命令，忽略
2025-08-04 14:39:27 | DEBUG | 收到消息: {'MsgId': 664104134, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_av3l5ovnw2o422:\n<msg><emoji fromusername="wxid_av3l5ovnw2o422" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289574, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_jM0aVRyk|v1_9wUU7Dpl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5034261993498180110, 'MsgSeq': 871424853}
2025-08-04 14:39:27 | INFO | 收到表情消息: 消息ID:664104134 来自:***********@chatroom 发送人:wxid_av3l5ovnw2o422 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:39:27 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5034261993498180110
2025-08-04 14:39:35 | DEBUG | 收到消息: {'MsgId': 1011859010, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaopingpingaaaa:\n<msg><emoji fromusername = "xiaopingpingaaaa" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len = "93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5 = "81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5 = "81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba00059a969f5071820000006e01004fb153482b5098b0b6d538ce4&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=fc5009ed417c5b0877a0fbbe50a48edf&amp;filekey=30440201010430302e02016e04025348042066633530303965643431376335623038373761306662626535306134386564660203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba000637669f5071820000006e02004fb253482b5098b0b6d538cf5&amp;ef=2&amp;bizid=1022" aeskey= "0ba7d7b6f5454278a19427ff0d8f9370" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0bf46ee41244b6f82af403e8b27cf3a0&amp;filekey=3043020101042f302d02016e040253480420306266343665653431323434623666383261663430336538623237636633613002022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba0006e58c9f5071820000006e03004fb353482b5098b0b6d538d06&amp;ef=3&amp;bizid=1022" externmd5 = "3c676d07f9799172e2482dea7ece99e5" width= "640" height= "559" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289582, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eEnj4JY/|v1_qcMwIfDB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3887734043433485643, 'MsgSeq': 871424854}
2025-08-04 14:39:35 | INFO | 收到表情消息: 消息ID:1011859010 来自:***********@chatroom 发送人:xiaopingpingaaaa MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:39:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3887734043433485643
2025-08-04 14:39:38 | DEBUG | 收到消息: {'MsgId': 1856635496, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_558jw1jndlum22:\n<msg><emoji fromusername = "wxid_558jw1jndlum22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len = "93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5 = "81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5 = "81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba00059a969f5071820000006e01004fb153482b5098b0b6d538ce4&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=fc5009ed417c5b0877a0fbbe50a48edf&amp;filekey=30440201010430302e02016e04025348042066633530303965643431376335623038373761306662626535306134386564660203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba000637669f5071820000006e02004fb253482b5098b0b6d538cf5&amp;ef=2&amp;bizid=1022" aeskey= "0ba7d7b6f5454278a19427ff0d8f9370" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0bf46ee41244b6f82af403e8b27cf3a0&amp;filekey=3043020101042f302d02016e040253480420306266343665653431323434623666383261663430336538623237636633613002022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d624ba0006e58c9f5071820000006e03004fb353482b5098b0b6d538d06&amp;ef=3&amp;bizid=1022" externmd5 = "3c676d07f9799172e2482dea7ece99e5" width= "640" height= "559" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289585, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_NDziZTuM|v1_kfdccfmG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5252866284117519454, 'MsgSeq': 871424855}
2025-08-04 14:39:38 | INFO | 收到表情消息: 消息ID:1856635496 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:39:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5252866284117519454
2025-08-04 14:39:44 | DEBUG | 收到消息: {'MsgId': 643466663, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n谢老'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289591, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_iopcG0rW|v1_mlGezQF9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7015598104876734976, 'MsgSeq': 871424856}
2025-08-04 14:39:44 | INFO | 收到文本消息: 消息ID:643466663 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:谢老
2025-08-04 14:39:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '谢老' from tianen532965049 in ***********@chatroom
2025-08-04 14:39:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['谢老']
2025-08-04 14:39:44 | DEBUG | 处理消息内容: '谢老'
2025-08-04 14:39:44 | DEBUG | 消息内容 '谢老' 不匹配任何命令，忽略
2025-08-04 14:39:46 | DEBUG | 收到消息: {'MsgId': 541208042, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="sysmsgtemplate">\n\t<sysmsgtemplate>\n\t\t<content_template type="tmpl_type_profile">\n\t\t\t<plain><![CDATA[]]></plain>\n\t\t\t<template><![CDATA["$adder$"通过扫描"$from$"分享的二维码加入群聊]]></template>\n\t\t\t<link_list>\n\t\t\t\t<link name="adder" type="link_profile">\n\t\t\t\t\t<memberlist>\n\t\t\t\t\t\t<member>\n\t\t\t\t\t\t\t<username><![CDATA[wxid_f9u3pot3q09x22]]></username>\n\t\t\t\t\t\t\t<nickname><![CDATA[ᨳᤢ小ღ幸⁵²⁰运༊]]></nickname>\n\t\t\t\t\t\t</member>\n\t\t\t\t\t</memberlist>\n\t\t\t\t</link>\n\t\t\t\t<link name="from" type="link_profile">\n\t\t\t\t\t<memberlist>\n\t\t\t\t\t\t<member>\n\t\t\t\t\t\t\t<username><![CDATA[wxid_ikxxrwasicud11]]></username>\n\t\t\t\t\t\t\t<nickname><![CDATA[゛花落ོ.°]]></nickname>\n\t\t\t\t\t\t</member>\n\t\t\t\t\t</memberlist>\n\t\t\t\t</link>\n\t\t\t</link_list>\n\t\t</content_template>\n\t</sysmsgtemplate>\n</sysmsg>\n'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289590, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5887747568180783955, 'MsgSeq': 871424857}
2025-08-04 14:39:46 | DEBUG | 系统消息类型: sysmsgtemplate
2025-08-04 14:39:47 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_f9u3pot3q09x22'] 内容:@ᨳᤢ小ღ幸⁵²⁰运༊ 欢迎加入Ｒ团❤
进群改下【游戏名】
1⃣️舞团技能升满、每天金币第二档签到+周贡不低于1.5w
2⃣️周一开启副本有空打打

3⃣️R团任务专用房（刷五局木材）
密码：11

群内严禁发小广告，禁止公屏骂架，管理在群内发现应立即劝解停止，以防牵扯进其他的团员，如有看见发生也请一定不要参与争吵，谢谢！🤝🤝🤝🤝
2025-08-04 14:39:49 | INFO | 发送app消息: 对方wxid:***********@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>欢迎加入！</title><des>ᨳᤢ小ღ幸⁵²⁰运༊</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3</lowdataurl><recorditem/><thumburl>http://shp.qpic.cn/collector/3211055935/d3e833ba-bddb-459b-97f9-edc7b3551af8/0</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric></songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://shp.qpic.cn/collector/3211055935/d3e833ba-bddb-459b-97f9-edc7b3551af8/0</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-08-04 14:39:50 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:28a4ea054d4192d888067ebd51b442d0 总长度:40461
2025-08-04 14:39:51 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:28a4ea054d4192d888067ebd51b442d0 总长度:40461
2025-08-04 14:39:53 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:28a4ea054d4192d888067ebd51b442d0 总长度:40461
2025-08-04 14:39:53 | DEBUG | 收到消息: {'MsgId': 2140327599, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289597, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_lLHQogPc|v1_/PFzw2Qu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8253653543353421779, 'MsgSeq': 871424862}
2025-08-04 14:39:53 | INFO | 收到表情消息: 消息ID:2140327599 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:39:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8253653543353421779
2025-08-04 14:39:58 | DEBUG | 收到消息: {'MsgId': 932797899, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289604, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_bytR6mq1|v1_OBmSrC/q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5094724044870564163, 'MsgSeq': 871424869}
2025-08-04 14:39:58 | INFO | 收到表情消息: 消息ID:932797899 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:39:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5094724044870564163
2025-08-04 14:39:58 | DEBUG | 收到消息: {'MsgId': 1578163697, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="181" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289605, 'MsgSource': '<msgsource>\n\t<signature>v1_NnXcn9mu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7667490975453524062, 'MsgSeq': 871424870}
2025-08-04 14:39:58 | DEBUG | 收到消息: {'MsgId': 1676361167, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289605, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_mO3i35qB|v1_MI+KBS59</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8687898345667971569, 'MsgSeq': 871424871}
2025-08-04 14:39:58 | INFO | 收到表情消息: 消息ID:1676361167 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:39:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8687898345667971569
2025-08-04 14:40:01 | DEBUG | 收到消息: {'MsgId': 1964603672, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_axt6et1ynj7l21:\n<msg><emoji fromusername = "wxid_axt6et1ynj7l21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ae9a74a3baf53b89ec13f45bb22f8e49" len = "90363" productid="com.tencent.xin.emoticon.person.stiker_1704711281300ba573346b4674" androidmd5="ae9a74a3baf53b89ec13f45bb22f8e49" androidlen="90363" s60v3md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v3len="90363" s60v5md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v5len="90363" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=ae9a74a3baf53b89ec13f45bb22f8e49&amp;filekey=30350201010421301f02020113040253480410ae9a74a3baf53b89ec13f45bb22f8e4902030160fb040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000e85ceb428aba60000011300004f50534803983bc1e66c48bee&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=bfd6a1b008c77623e61620a57cc099ac&amp;filekey=30340201010420301e02020113040253480410bfd6a1b008c77623e61620a57cc099ac02024745040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000cf5b0b428aba60000011300004f5053481ba3f031565b1c487&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f7ced4c31f0723b3e9d56e3d1be8ca53&amp;filekey=30350201010421301f02020106040253480410f7ced4c31f0723b3e9d56e3d1be8ca530203016100040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e150006d2b0b428aba60000010600004f50534822e871b1565eb6d16&amp;bizid=1023" aeskey= "64d5bc31811c277facceb558d1ab38c5" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=23431bddf4bab1067b4a428f8fc54721&amp;filekey=30340201010420301e020201060402535a041023431bddf4bab1067b4a428f8fc5472102024e80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2659d12630002334b41362a010000010600004f50535a1ad69bc1e6631ab09&amp;bizid=1023" externmd5 = "78739e741d7e895089edf060198587d8" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChcKB2RlZmF1bHQSDOiwouiwouiAgeadvw==" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289608, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_jgAE54Gx|v1_bym6d9mE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6736765690658294606, 'MsgSeq': 871424872}
2025-08-04 14:40:01 | INFO | 收到表情消息: 消息ID:1964603672 来自:***********@chatroom 发送人:wxid_axt6et1ynj7l21 MD5:ae9a74a3baf53b89ec13f45bb22f8e49 大小:90363
2025-08-04 14:40:01 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6736765690658294606
2025-08-04 14:40:03 | DEBUG | 收到消息: {'MsgId': 577005971, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="182" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289611, 'MsgSource': '<msgsource>\n\t<signature>v1_AeUraOYv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1834279860073964541, 'MsgSeq': 871424873}
2025-08-04 14:40:11 | DEBUG | 收到消息: {'MsgId': 582725312, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_axt6et1ynj7l21:\n<msg><emoji fromusername = "wxid_axt6et1ynj7l21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289618, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_vY8Dvf4q|v1_OomF+246</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5200752464155268621, 'MsgSeq': 871424874}
2025-08-04 14:40:11 | INFO | 收到表情消息: 消息ID:582725312 来自:***********@chatroom 发送人:wxid_axt6et1ynj7l21 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5200752464155268621
2025-08-04 14:40:17 | DEBUG | 收到消息: {'MsgId': 36313344, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n<msg><emoji fromusername = "wxid_xfxd40diz3bd22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289624, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_18JR4Odv|v1_31MYMyM2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6218826563864590785, 'MsgSeq': 871424875}
2025-08-04 14:40:17 | INFO | 收到表情消息: 消息ID:36313344 来自:***********@chatroom 发送人:wxid_xfxd40diz3bd22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6218826563864590785
2025-08-04 14:40:20 | DEBUG | 收到消息: {'MsgId': 1033528567, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_2ztoui7te69r22:\n<msg><emoji fromusername="wxid_2ztoui7te69r22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289627, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_1JB1NsEn|v1_CO0Og6iZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5377630197675087086, 'MsgSeq': 871424876}
2025-08-04 14:40:20 | INFO | 收到表情消息: 消息ID:1033528567 来自:***********@chatroom 发送人:wxid_2ztoui7te69r22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5377630197675087086
2025-08-04 14:40:31 | DEBUG | 收到消息: {'MsgId': 1673157131, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wangchunmeng7291:\n<msg><emoji fromusername = "wangchunmeng7291" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289638, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_lXPpIPJv|v1_9gYTnHYi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 612927532509311111, 'MsgSeq': 871424877}
2025-08-04 14:40:31 | INFO | 收到表情消息: 消息ID:1673157131 来自:***********@chatroom 发送人:wangchunmeng7291 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 612927532509311111
2025-08-04 14:40:33 | DEBUG | 收到消息: {'MsgId': 794647739, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289640, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_0m58Qry1|v1_tDWaba04</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6521202801810592296, 'MsgSeq': 871424878}
2025-08-04 14:40:33 | INFO | 收到表情消息: 消息ID:794647739 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6521202801810592296
2025-08-04 14:40:53 | DEBUG | 收到消息: {'MsgId': 8328655, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_558jw1jndlum22:\n<msg><emoji fromusername = "wxid_558jw1jndlum22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289660, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_egpxgp+/|v1_VhLWSSYn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2830951416959933387, 'MsgSeq': 871424879}
2025-08-04 14:40:53 | INFO | 收到表情消息: 消息ID:8328655 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2830951416959933387
2025-08-04 14:40:57 | DEBUG | 收到消息: {'MsgId': 189504411, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<msg><emoji fromusername="wxid_5kipwrzramxr22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289664, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_eW5zjMzh|v1_V1Uv7YUJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8936883762641768378, 'MsgSeq': 871424880}
2025-08-04 14:40:57 | INFO | 收到表情消息: 消息ID:189504411 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8936883762641768378
2025-08-04 14:40:58 | DEBUG | 收到消息: {'MsgId': 1213169107, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_558jw1jndlum22:\n<msg><emoji fromusername = "wxid_558jw1jndlum22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289665, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_ifcmhgOG|v1_8XLvf26V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 222855426046555542, 'MsgSeq': 871424881}
2025-08-04 14:40:58 | INFO | 收到表情消息: 消息ID:1213169107 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 222855426046555542
2025-08-04 14:40:58 | DEBUG | 收到消息: {'MsgId': 1580783659, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_558jw1jndlum22:\n<msg><emoji fromusername = "wxid_558jw1jndlum22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len = "40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5 = "28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5 = "28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f0202010604025348041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303464366463303030303030303061336464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f02020106040253480410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333039353532353030303835623663303030303030303063396432393630393030303030313036&amp;bizid=1023" aeskey= "b50b448dd0244f95adba089bba3281f0" externurl = "" externmd5 = "" width= "442" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289665, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_ifcmhgOG|v1_8XLvf26V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2005166935985077106, 'MsgSeq': 871424882}
2025-08-04 14:40:58 | INFO | 收到表情消息: 消息ID:1580783659 来自:***********@chatroom 发送人:wxid_558jw1jndlum22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:40:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2005166935985077106
2025-08-04 14:41:16 | DEBUG | 收到消息: {'MsgId': 491366090, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_2ztoui7te69r22</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_xfxd40diz3bd22</pattedusername>\n  <patsuffix><![CDATA[真的很无语。]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_2ztoui7te69r22}" 拍了拍 "${wxid_xfxd40diz3bd22}" 真的很无语。]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289681, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4433734974519007199, 'MsgSeq': 871424883}
2025-08-04 14:41:16 | DEBUG | 系统消息类型: pat
2025-08-04 14:41:16 | INFO | 收到拍一拍消息: 消息ID:491366090 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_2ztoui7te69r22 被拍:wxid_xfxd40diz3bd22 后缀:真的很无语。
2025-08-04 14:41:16 | DEBUG | [PatReply] 被拍者 wxid_xfxd40diz3bd22 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-08-04 14:41:25 | DEBUG | 收到消息: {'MsgId': 406884489, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_2ztoui7te69r22:\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>1512007834</msgid><newmsgid>4433734974519007199</newmsgid><replacemsg><![CDATA["深海璃雾" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289689, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5799085677676345439, 'MsgSeq': 871424884}
2025-08-04 14:41:25 | DEBUG | 系统消息类型: revokemsg
2025-08-04 14:41:25 | INFO | 未知的系统消息类型: {'MsgId': 406884489, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>***********@chatroom</session><msgid>1512007834</msgid><newmsgid>4433734974519007199</newmsgid><replacemsg><![CDATA["深海璃雾" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289689, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5799085677676345439, 'MsgSeq': 871424884, 'FromWxid': '***********@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_2ztoui7te69r22'}
2025-08-04 14:41:57 | DEBUG | 收到消息: {'MsgId': 260598724, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_b4fob2zyc4kq22:\n<msg><emoji fromusername="wxid_b4fob2zyc4kq22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289724, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_43SSpKaa|v1_Gd9W7Pml</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7038251359055041619, 'MsgSeq': 871424885}
2025-08-04 14:41:57 | INFO | 收到表情消息: 消息ID:260598724 来自:***********@chatroom 发送人:wxid_b4fob2zyc4kq22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:41:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7038251359055041619
2025-08-04 14:41:59 | DEBUG | 收到消息: {'MsgId': 643812045, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_b4fob2zyc4kq22:\n<msg><emoji fromusername="wxid_b4fob2zyc4kq22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289726, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_rrjWczn2|v1_dGZNYMLv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6606646095446868798, 'MsgSeq': 871424886}
2025-08-04 14:41:59 | INFO | 收到表情消息: 消息ID:643812045 来自:***********@chatroom 发送人:wxid_b4fob2zyc4kq22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:41:59 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6606646095446868798
2025-08-04 14:42:07 | DEBUG | 收到消息: {'MsgId': 134290196, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289734, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_nMSH87XC|v1_+T7uLdic</signature>\n</msgsource>\n', 'NewMsgId': 334024850092330821, 'MsgSeq': 871424887}
2025-08-04 14:42:07 | INFO | 收到表情消息: 消息ID:134290196 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:42:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 334024850092330821
2025-08-04 14:42:11 | DEBUG | 收到消息: {'MsgId': 878363572, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<msg><emoji fromusername="wxid_ohq9p1qosjzq22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="28a4ea054d4192d888067ebd51b442d0" len="40461" productid="" androidmd5="28a4ea054d4192d888067ebd51b442d0" androidlen="40461" s60v3md5="28a4ea054d4192d888067ebd51b442d0" s60v3len="40461" s60v5md5="28a4ea054d4192d888067ebd51b442d0" s60v5len="40461" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=28a4ea054d4192d888067ebd51b442d0&amp;filekey=30350201010421301f020201060402535a041028a4ea054d4192d888067ebd51b442d00203009e0d040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130323030306563306664353039333365636137373239353630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=956ab84282e0bced3ce7e6026954c5d3&amp;filekey=30350201010421301f020201060402535a0410956ab84282e0bced3ce7e6026954c5d30203009e10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363033353130333030303134346636353039333365636161313536353830393030303030313036&amp;bizid=1023" aeskey="b50b448dd0244f95adba089bba3281f0" externurl="" externmd5="" width="442" height="95" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChflsJYg6aG2IOWboumYn+asoui/juaCqA==" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289739, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_UTmCOKIP|v1_q/utPta0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4296979662460952408, 'MsgSeq': 871424888}
2025-08-04 14:42:11 | INFO | 收到表情消息: 消息ID:878363572 来自:***********@chatroom 发送人:wxid_ohq9p1qosjzq22 MD5:28a4ea054d4192d888067ebd51b442d0 大小:40461
2025-08-04 14:42:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4296979662460952408
2025-08-04 14:42:16 | DEBUG | 收到消息: {'MsgId': 816739074, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n有啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289743, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_bsV5vNFM|v1_UShFytP7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 有啊', 'NewMsgId': 6952188344389317158, 'MsgSeq': 871424889}
2025-08-04 14:42:16 | INFO | 收到文本消息: 消息ID:816739074 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:有啊
2025-08-04 14:42:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有啊' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:42:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['有啊']
2025-08-04 14:42:17 | DEBUG | 处理消息内容: '有啊'
2025-08-04 14:42:17 | DEBUG | 消息内容 '有啊' 不匹配任何命令，忽略
2025-08-04 14:43:27 | DEBUG | 收到消息: {'MsgId': 1860319424, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg>\n\t<appmsg appid="" sdkver="">\n\t\t<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>\n\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>\n\t\t<lowurl><![CDATA[]]></lowurl>\n\t\t<type><![CDATA[2001]]></type>\n\t\t<title><![CDATA[微信红包]]></title>\n\t\t<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>\n\t\t<wcpayinfo>\n\t\t\t<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>\n\t\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>\n\t\t\t<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>\n\t\t\t<receivertitle><![CDATA[欢迎 等风来]]></receivertitle>\n\t\t\t<sendertitle><![CDATA[欢迎 等风来]]></sendertitle>\n\t\t\t<scenetext><![CDATA[微信红包]]></scenetext>\n\t\t\t<senderdes><![CDATA[查看红包]]></senderdes>\n\t\t\t<receiverdes><![CDATA[领取红包]]></receiverdes>\n\t\t\t<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047336759636026&sendusername=wxid_ikxxrwasicud11&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b&total_num=10]]></nativeurl>\n\t\t\t<sceneid><![CDATA[1002]]></sceneid>\n\t\t\t<innertype><![CDATA[0]]></innertype>\n\t\t\t<paymsgid><![CDATA[1000039901202508047336759636026]]></paymsgid>\n\t\t\t<scenetext>微信红包</scenetext>\n\t\t\t<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>\n\t\t\t<invalidtime><![CDATA[1754376213]]></invalidtime>\n\t\t\t<broaden />\n\t\t</wcpayinfo>\n\t</appmsg>\n\t<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289814, 'MsgSource': '<msgsource>\n\t<pushkey />\n\t<ModifyMsgAction />\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_qlmJknn8|v1_/eX8W7Ti</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8423393765012179073, 'MsgSeq': 871424890}
2025-08-04 14:43:27 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-08-04 14:43:27 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[欢迎 等风来]]></receivertitle>
			<sendertitle><![CDATA[欢迎 等风来]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047336759636026&sendusername=wxid_ikxxrwasicud11&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202508047336759636026]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1754376213]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-08-04 14:43:27 | DEBUG | XML消息类型: 2001
2025-08-04 14:43:27 | DEBUG | XML消息标题: 微信红包
2025-08-04 14:43:27 | DEBUG | XML消息描述: 我给你发了一个红包，赶紧去拆!
2025-08-04 14:43:27 | DEBUG | XML消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b
2025-08-04 14:43:27 | DEBUG | XML消息缩略图URL: https://wx.gtimg.com/hongbao/1800/hb.png
2025-08-04 14:43:27 | INFO | 未知的XML消息类型: 2001
2025-08-04 14:43:27 | INFO | 消息标题: 微信红包
2025-08-04 14:43:27 | INFO | 消息描述: 我给你发了一个红包，赶紧去拆!
2025-08-04 14:43:27 | INFO | 消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b
2025-08-04 14:43:27 | INFO | 完整XML内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202508047336759636026&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[欢迎 等风来]]></receivertitle>
			<sendertitle><![CDATA[欢迎 等风来]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202508047336759636026&sendusername=wxid_ikxxrwasicud11&ver=6&sign=7f70f056deaef5fa0e3f5e90dd34b720b46c6f74a7012d7d8870c58478d808e74416e4b270637d3377de7cac536a65252501039c94b702caf93bb7e162e9720816cbe2ca6a3546ca53c9b412f98b124b&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202508047336759636026]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1754376213]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-08-04 14:43:32 | DEBUG | 收到消息: {'MsgId': 1307205360, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289819, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_yuURBZJM|v1_Af5m23XE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4402994905597612994, 'MsgSeq': 871424891}
2025-08-04 14:43:32 | INFO | 收到表情消息: 消息ID:1307205360 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:43:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4402994905597612994
2025-08-04 14:43:37 | DEBUG | 收到消息: {'MsgId': 24432892, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_2ztoui7te69r22:\n<msg><emoji fromusername="wxid_2ztoui7te69r22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="377b2c64b7142899a1df2c285f32bd40" len="37737" productid="" androidmd5="377b2c64b7142899a1df2c285f32bd40" androidlen="37737" s60v3md5="377b2c64b7142899a1df2c285f32bd40" s60v3len="37737" s60v5md5="377b2c64b7142899a1df2c285f32bd40" s60v5len="37737" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=377b2c64b7142899a1df2c285f32bd40&amp;filekey=30440201010430302e02016e04025348042033373762326336346237313432383939613164663263323835663332626434300203009369040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313032303131303530333130303065336139356337636238383266646661303531303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6e77c5997cd2603d376c90fd4ee32aa5&amp;filekey=30440201010430302e02016e04025348042036653737633539393763643236303364333736633930666434656533326161350203009370040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313032303131303530333230303030313233616337636238383266646661303531303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="273df718ff3c4f9db504339a3b034ee8" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=51c1d6e0e1ad65bbffffe64da467f81c&amp;filekey=3043020101042f302d02016e040253480420353163316436653065316164363562626666666665363464613436376638316302025410040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313032303131303530333230303031323935386337636238383266646661303531303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="29f766f9ee5276b92a8810c49dab842b" width="640" height="195" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="CgzosKLosKLogIHmnb8=" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289825, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_cyg0+zAo|v1_U++1aOr6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6486964576141570885, 'MsgSeq': 871424892}
2025-08-04 14:43:37 | INFO | 收到表情消息: 消息ID:24432892 来自:***********@chatroom 发送人:wxid_2ztoui7te69r22 MD5:377b2c64b7142899a1df2c285f32bd40 大小:37737
2025-08-04 14:43:37 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6486964576141570885
2025-08-04 14:43:38 | DEBUG | 收到消息: {'MsgId': 1307286957, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<msg><emoji fromusername="wxid_5kipwrzramxr22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289825, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_03Dfcgj8|v1_udKKEqYZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2646729406763937928, 'MsgSeq': 871424893}
2025-08-04 14:43:38 | INFO | 收到表情消息: 消息ID:1307286957 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:43:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2646729406763937928
2025-08-04 14:43:38 | DEBUG | 收到消息: {'MsgId': 405224297, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_av3l5ovnw2o422:\n<msg><emoji fromusername="wxid_av3l5ovnw2o422" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289825, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_QtEvV65/|v1_kGTKTLKj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4996671588641658886, 'MsgSeq': 871424894}
2025-08-04 14:43:38 | INFO | 收到表情消息: 消息ID:405224297 来自:***********@chatroom 发送人:wxid_av3l5ovnw2o422 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:43:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4996671588641658886
2025-08-04 14:43:43 | DEBUG | 收到消息: {'MsgId': 1287119767, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289830, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_PtGS4nbx|v1_AxydlfB3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3933196561726016170, 'MsgSeq': 871424895}
2025-08-04 14:43:43 | INFO | 收到表情消息: 消息ID:1287119767 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:43:43 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3933196561726016170
2025-08-04 14:43:43 | DEBUG | 收到消息: {'MsgId': 293223556, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ae9a74a3baf53b89ec13f45bb22f8e49" len = "90363" productid="com.tencent.xin.emoticon.person.stiker_1704711281300ba573346b4674" androidmd5="ae9a74a3baf53b89ec13f45bb22f8e49" androidlen="90363" s60v3md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v3len="90363" s60v5md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v5len="90363" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=ae9a74a3baf53b89ec13f45bb22f8e49&amp;filekey=30350201010421301f02020113040253480410ae9a74a3baf53b89ec13f45bb22f8e4902030160fb040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000e85ceb428aba60000011300004f50534803983bc1e66c48bee&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=bfd6a1b008c77623e61620a57cc099ac&amp;filekey=30340201010420301e02020113040253480410bfd6a1b008c77623e61620a57cc099ac02024745040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000cf5b0b428aba60000011300004f5053481ba3f031565b1c487&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f7ced4c31f0723b3e9d56e3d1be8ca53&amp;filekey=30350201010421301f02020106040253480410f7ced4c31f0723b3e9d56e3d1be8ca530203016100040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e150006d2b0b428aba60000010600004f50534822e871b1565eb6d16&amp;bizid=1023" aeskey= "64d5bc31811c277facceb558d1ab38c5" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=23431bddf4bab1067b4a428f8fc54721&amp;filekey=30340201010420301e020201060402535a041023431bddf4bab1067b4a428f8fc5472102024e80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2659d12630002334b41362a010000010600004f50535a1ad69bc1e6631ab09&amp;bizid=1023" externmd5 = "78739e741d7e895089edf060198587d8" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChcKB2RlZmF1bHQSDOiwouiwouiAgeadvw==" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289830, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_sWZ493QR|v1_UDHpIbTg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2686252789744006260, 'MsgSeq': 871424896}
2025-08-04 14:43:43 | INFO | 收到表情消息: 消息ID:293223556 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 MD5:ae9a74a3baf53b89ec13f45bb22f8e49 大小:90363
2025-08-04 14:43:43 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2686252789744006260
2025-08-04 14:43:48 | DEBUG | 收到消息: {'MsgId': 1478155931, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n谢老但没到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289835, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_jgQiaxOd|v1_2/GKNAK9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8451441529482084540, 'MsgSeq': 871424897}
2025-08-04 14:43:48 | INFO | 收到文本消息: 消息ID:1478155931 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:谢老但没到
2025-08-04 14:43:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '谢老但没到' from tianen532965049 in ***********@chatroom
2025-08-04 14:43:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['谢老但没到']
2025-08-04 14:43:48 | DEBUG | 处理消息内容: '谢老但没到'
2025-08-04 14:43:48 | DEBUG | 消息内容 '谢老但没到' 不匹配任何命令，忽略
2025-08-04 14:43:50 | DEBUG | 收到消息: {'MsgId': 963897529, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n<msg><emoji fromusername = "wxid_xfxd40diz3bd22" tousername = "***********@chatroom" type="3" idbuffer="media:0_0" md5="b3cc17ec70ac08e89769fc99cb02f674" len = "49961" productid="" androidmd5="b3cc17ec70ac08e89769fc99cb02f674" androidlen="49961" s60v3md5 = "b3cc17ec70ac08e89769fc99cb02f674" s60v3len="49961" s60v5md5 = "b3cc17ec70ac08e89769fc99cb02f674" s60v5len="49961" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=b3cc17ec70ac08e89769fc99cb02f674&amp;filekey=30440201010430302e02016e0402534804206233636331376563373061633038653839373639666339396362303266363734020300c329040d00000004627466730000000132&amp;hy=SH&amp;storeid=26842a6f20001bf5074265f3e0000006e01004fb1534813765b01e69567612&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=dde5b1df14abe5de5ea4ee3162d20796&amp;filekey=30440201010430302e02016e0402534804206464653562316466313461626535646535656134656533313632643230373936020300c330040d00000004627466730000000132&amp;hy=SH&amp;storeid=26842a6f20002e06074265f3e0000006e02004fb2534813765b01e6956762d&amp;ef=2&amp;bizid=1022" aeskey= "bc64c57db39d4e42b310f1f46985b993" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=c7f0311bb30a414008a96b978fea9cb0&amp;filekey=3043020101042f302d02016e040253480420633766303331316262333061343134303038613936623937386665613963623002026690040d00000004627466730000000132&amp;hy=SH&amp;storeid=26842a6f20003cd0e74265f3e0000006e03004fb3534813765b01e69567644&amp;ef=3&amp;bizid=1022" externmd5 = "47b44357769a3540a8284be9dcba55e6" width= "606" height= "606" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289837, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_SN90hg46|v1_XPs/5Pre</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4174129663539199356, 'MsgSeq': 871424898}
2025-08-04 14:43:50 | INFO | 收到表情消息: 消息ID:963897529 来自:***********@chatroom 发送人:wxid_xfxd40diz3bd22 MD5:b3cc17ec70ac08e89769fc99cb02f674 大小:49961
2025-08-04 14:43:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4174129663539199356
2025-08-04 14:43:50 | DEBUG | 收到消息: {'MsgId': 1199006299, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_axt6et1ynj7l21:\n<msg><emoji fromusername = "wxid_axt6et1ynj7l21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ae9a74a3baf53b89ec13f45bb22f8e49" len = "90363" productid="com.tencent.xin.emoticon.person.stiker_1704711281300ba573346b4674" androidmd5="ae9a74a3baf53b89ec13f45bb22f8e49" androidlen="90363" s60v3md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v3len="90363" s60v5md5 = "ae9a74a3baf53b89ec13f45bb22f8e49" s60v5len="90363" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=ae9a74a3baf53b89ec13f45bb22f8e49&amp;filekey=30350201010421301f02020113040253480410ae9a74a3baf53b89ec13f45bb22f8e4902030160fb040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000e85ceb428aba60000011300004f50534803983bc1e66c48bee&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=bfd6a1b008c77623e61620a57cc099ac&amp;filekey=30340201010420301e02020113040253480410bfd6a1b008c77623e61620a57cc099ac02024745040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e14000cf5b0b428aba60000011300004f5053481ba3f031565b1c487&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f7ced4c31f0723b3e9d56e3d1be8ca53&amp;filekey=30350201010421301f02020106040253480410f7ced4c31f0723b3e9d56e3d1be8ca530203016100040d00000004627466730000000132&amp;hy=SH&amp;storeid=2659d0e150006d2b0b428aba60000010600004f50534822e871b1565eb6d16&amp;bizid=1023" aeskey= "64d5bc31811c277facceb558d1ab38c5" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=23431bddf4bab1067b4a428f8fc54721&amp;filekey=30340201010420301e020201060402535a041023431bddf4bab1067b4a428f8fc5472102024e80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2659d12630002334b41362a010000010600004f50535a1ad69bc1e6631ab09&amp;bizid=1023" externmd5 = "78739e741d7e895089edf060198587d8" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChcKB2RlZmF1bHQSDOiwouiwouiAgeadvw==" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289837, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_SJrWCo08|v1_ubQWU/jM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1030982325142768721, 'MsgSeq': 871424899}
2025-08-04 14:43:50 | INFO | 收到表情消息: 消息ID:1199006299 来自:***********@chatroom 发送人:wxid_axt6et1ynj7l21 MD5:ae9a74a3baf53b89ec13f45bb22f8e49 大小:90363
2025-08-04 14:43:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1030982325142768721
2025-08-04 14:43:54 | DEBUG | 收到消息: {'MsgId': 1087943026, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_i50qglccgxmo12:\n<msg><emoji fromusername="wxid_i50qglccgxmo12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289841, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_4+zpgo2L|v1_3/FI3Yl2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8434425924872925531, 'MsgSeq': 871424900}
2025-08-04 14:43:54 | INFO | 收到表情消息: 消息ID:1087943026 来自:***********@chatroom 发送人:wxid_i50qglccgxmo12 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-08-04 14:43:54 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8434425924872925531
2025-08-04 14:44:38 | DEBUG | 收到消息: {'MsgId': 573808339, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>好的好的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1002903553370994393</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_snv13qf05qjx11</chatusr>\n\t\t\t<displayname>夕未语</displayname>\n\t\t\t<content>这里禁止代练行为 封号了我们负担不起</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;866643039&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;147&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_pe1ILkaA|v1_+ODVrT1f&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754289135</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_cb2tqz5n94lp12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289885, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a6e62e7069ce2f7898ed541533358eb0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_W9chubFI|v1_0f9WE4TQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7864807046306583822, 'MsgSeq': 871424901}
2025-08-04 14:44:38 | DEBUG | 从群聊消息中提取发送者: wxid_cb2tqz5n94lp12
2025-08-04 14:44:38 | DEBUG | 使用已解析的XML处理引用消息
2025-08-04 14:44:38 | INFO | 收到引用消息: 消息ID:573808339 来自:***********@chatroom 发送人:wxid_cb2tqz5n94lp12 内容:好的好的 引用类型:1
2025-08-04 14:44:38 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-04 14:44:38 | INFO | [DouBaoImageToImage] 消息内容: '好的好的' from wxid_cb2tqz5n94lp12 in ***********@chatroom
2025-08-04 14:44:38 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['好的好的']
2025-08-04 14:44:38 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-04 14:44:38 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-04 14:44:38 | INFO |   - 消息内容: 好的好的
2025-08-04 14:44:38 | INFO |   - 群组ID: ***********@chatroom
2025-08-04 14:44:38 | INFO |   - 发送人: wxid_cb2tqz5n94lp12
2025-08-04 14:44:38 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '这里禁止代练行为 封号了我们负担不起', 'Msgid': '1002903553370994393', 'NewMsgId': '1002903553370994393', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '夕未语', 'MsgSource': '<msgsource><sequence_id>866643039</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>147</membercount>\n\t<signature>N0_V1_pe1ILkaA|v1_+ODVrT1f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754289135', 'SenderWxid': 'wxid_cb2tqz5n94lp12'}
2025-08-04 14:44:38 | INFO |   - 引用消息ID: 
2025-08-04 14:44:38 | INFO |   - 引用消息类型: 
2025-08-04 14:44:38 | INFO |   - 引用消息内容: 这里禁止代练行为 封号了我们负担不起
2025-08-04 14:44:38 | INFO |   - 引用消息发送人: wxid_cb2tqz5n94lp12
2025-08-04 14:44:52 | DEBUG | 收到消息: {'MsgId': 594650537, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n这tm还有三四天，折磨死了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289899, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_72lvMc23|v1_vfut7lKY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3298502431590766849, 'MsgSeq': 871424902}
2025-08-04 14:44:52 | INFO | 收到文本消息: 消息ID:594650537 来自:***********@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:这tm还有三四天，折磨死了
2025-08-04 14:44:52 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这tm还有三四天，折磨死了' from wxid_bmzp9achod6922 in ***********@chatroom
2025-08-04 14:44:52 | DEBUG | [DouBaoImageToImage] 命令解析: ['这tm还有三四天，折磨死了']
2025-08-04 14:44:52 | DEBUG | 处理消息内容: '这tm还有三四天，折磨死了'
2025-08-04 14:44:52 | DEBUG | 消息内容 '这tm还有三四天，折磨死了' 不匹配任何命令，忽略
2025-08-04 14:45:46 | DEBUG | 收到消息: {'MsgId': 609797292, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'poison3-1:\n@锦岚\u2005大佬来个链接'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289953, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_1I4Ztj8l|v1_koak+Tfs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '6400 : @锦岚\u2005大佬来个链接', 'NewMsgId': 1366280997136875309, 'MsgSeq': 871424903}
2025-08-04 14:45:46 | INFO | 收到文本消息: 消息ID:609797292 来自:***********@chatroom 发送人:poison3-1 @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 大佬来个链接
2025-08-04 14:45:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@锦岚 大佬来个链接' from poison3-1 in ***********@chatroom
2025-08-04 14:45:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['@锦岚\u2005大佬来个链接']
2025-08-04 14:45:47 | DEBUG | 处理消息内容: '@锦岚 大佬来个链接'
2025-08-04 14:45:47 | DEBUG | 消息内容 '@锦岚 大佬来个链接' 不匹配任何命令，忽略
2025-08-04 14:46:18 | DEBUG | 收到消息: {'MsgId': 2057108478, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n反正号里钻都清了[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289985, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_oxMCXi4h|v1_C93vSizd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8060744375391924142, 'MsgSeq': 871424904}
2025-08-04 14:46:18 | INFO | 收到文本消息: 消息ID:2057108478 来自:***********@chatroom 发送人:wxid_cb2tqz5n94lp12 @:[] 内容:反正号里钻都清了[破涕为笑]
2025-08-04 14:46:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '反正号里钻都清了[破涕为笑]' from wxid_cb2tqz5n94lp12 in ***********@chatroom
2025-08-04 14:46:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['反正号里钻都清了[破涕为笑]']
2025-08-04 14:46:18 | DEBUG | 处理消息内容: '反正号里钻都清了[破涕为笑]'
2025-08-04 14:46:18 | DEBUG | 消息内容 '反正号里钻都清了[破涕为笑]' 不匹配任何命令，忽略
2025-08-04 14:46:21 | DEBUG | 收到消息: {'MsgId': 1831187922, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_y89gujlu6ed422:\n来财'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754289988, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_0L6r44K4|v1_ttLDeo/r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '.渝. : 来财', 'NewMsgId': 3909564742748369699, 'MsgSeq': 871424905}
2025-08-04 14:46:21 | INFO | 收到文本消息: 消息ID:1831187922 来自:***********@chatroom 发送人:wxid_y89gujlu6ed422 @:[] 内容:来财
2025-08-04 14:46:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '来财' from wxid_y89gujlu6ed422 in ***********@chatroom
2025-08-04 14:46:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['来财']
2025-08-04 14:46:22 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:512770d311d177911fa10e42d4162ffd 总长度:9992069
2025-08-04 14:46:22 | DEBUG | 处理消息内容: '来财'
2025-08-04 14:46:22 | DEBUG | 消息内容 '来财' 不匹配任何命令，忽略
2025-08-04 14:46:38 | DEBUG | 收到消息: {'MsgId': 1408777729, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_y89gujlu6ed422:\n跑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290005, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_JBYg33bd|v1_jkzwORrI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '.渝. : 跑', 'NewMsgId': 9198466675780362641, 'MsgSeq': 871424908}
2025-08-04 14:46:38 | INFO | 收到文本消息: 消息ID:1408777729 来自:***********@chatroom 发送人:wxid_y89gujlu6ed422 @:[] 内容:跑
2025-08-04 14:46:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '跑' from wxid_y89gujlu6ed422 in ***********@chatroom
2025-08-04 14:46:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['跑']
2025-08-04 14:46:38 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:c6f7107246af62c52f26daddedd56340 总长度:9992069
2025-08-04 14:46:38 | DEBUG | 处理消息内容: '跑'
2025-08-04 14:46:38 | DEBUG | 消息内容 '跑' 不匹配任何命令，忽略
2025-08-04 14:46:41 | DEBUG | 收到消息: {'MsgId': 1544598035, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6ac5b451c24bcedf304d35e123ebba67" encryver="1" cdnthumbaeskey="6ac5b451c24bcedf304d35e123ebba67" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" cdnthumblength="4366" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" length="145655" md5="cfb7b1a51c1f175ab5de111fe4a58361" hevc_mid_size="145655" originsourcemd5="27b7c0957b23d29108e70ec7825a603f">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY1OTEwMDAwNDAwMDAwMDAiLCJwZHFoYXNoIjoiMzNjMzAzZTM1N2U4ZTM2M2E3NTg5ZDM2MmFiZDBjYmUyYWZjYjgwMzNmNjJmMDIzMTVmMmY0MWJlODE2ODI1YyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290008, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>77a229cb69a17752381e7d07fac7e45d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_nTuMs3nm|v1_e3SXYcpP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6216487877801470913, 'MsgSeq': 871424911}
2025-08-04 14:46:41 | INFO | 收到图片消息: 消息ID:1544598035 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 XML:<?xml version="1.0"?><msg><img aeskey="6ac5b451c24bcedf304d35e123ebba67" encryver="1" cdnthumbaeskey="6ac5b451c24bcedf304d35e123ebba67" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" cdnthumblength="4366" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" length="145655" md5="cfb7b1a51c1f175ab5de111fe4a58361" hevc_mid_size="145655" originsourcemd5="27b7c0957b23d29108e70ec7825a603f"><secHashInfoBase64>eyJwaGFzaCI6ImY1OTEwMDAwNDAwMDAwMDAiLCJwZHFoYXNoIjoiMzNjMzAzZTM1N2U4ZTM2M2E3NTg5ZDM2MmFiZDBjYmUyYWZjYjgwMzNmNjJmMDIzMTVmMmY0MWJlODE2ODI1YyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:46:41 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:46:41 | INFO | [TimerTask] 缓存图片消息: 1544598035
2025-08-04 14:46:47 | DEBUG | 收到消息: {'MsgId': 887082874, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n有人在上面吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290014, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_VAJ6/MyU|v1_ARPmCUuO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5196338206989584645, 'MsgSeq': 871424912}
2025-08-04 14:46:47 | INFO | 收到文本消息: 消息ID:887082874 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:有人在上面吗
2025-08-04 14:46:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有人在上面吗' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:46:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['有人在上面吗']
2025-08-04 14:46:47 | DEBUG | 处理消息内容: '有人在上面吗'
2025-08-04 14:46:47 | DEBUG | 消息内容 '有人在上面吗' 不匹配任何命令，忽略
2025-08-04 14:46:51 | DEBUG | 收到消息: {'MsgId': 434176884, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n拽我下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290018, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_tc5K5y6s|v1_HOfNr+ip</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1780862353327181133, 'MsgSeq': 871424913}
2025-08-04 14:46:51 | INFO | 收到文本消息: 消息ID:434176884 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:拽我下
2025-08-04 14:46:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '拽我下' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:46:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['拽我下']
2025-08-04 14:46:51 | DEBUG | 处理消息内容: '拽我下'
2025-08-04 14:46:51 | DEBUG | 消息内容 '拽我下' 不匹配任何命令，忽略
2025-08-04 14:47:05 | DEBUG | 收到消息: {'MsgId': 343806767, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n不想努力了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290030, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_C1IZ2maW|v1_SK386OQx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4269759430418383547, 'MsgSeq': 871424914}
2025-08-04 14:47:05 | INFO | 收到文本消息: 消息ID:343806767 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:不想努力了
2025-08-04 14:47:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不想努力了' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:47:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['不想努力了']
2025-08-04 14:47:05 | DEBUG | 处理消息内容: '不想努力了'
2025-08-04 14:47:05 | DEBUG | 消息内容 '不想努力了' 不匹配任何命令，忽略
2025-08-04 14:47:18 | DEBUG | 收到消息: {'MsgId': 734520773, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_vuywamzgu2z012:\n三阶翅膀飞一下应该就可以了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290045, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_QnNmGUe9|v1_3uVdGawf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 832485995590920600, 'MsgSeq': 871424915}
2025-08-04 14:47:18 | INFO | 收到文本消息: 消息ID:734520773 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 @:[] 内容:三阶翅膀飞一下应该就可以了
2025-08-04 14:47:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '三阶翅膀飞一下应该就可以了' from wxid_vuywamzgu2z012 in ***********@chatroom
2025-08-04 14:47:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['三阶翅膀飞一下应该就可以了']
2025-08-04 14:47:18 | DEBUG | 处理消息内容: '三阶翅膀飞一下应该就可以了'
2025-08-04 14:47:18 | DEBUG | 消息内容 '三阶翅膀飞一下应该就可以了' 不匹配任何命令，忽略
2025-08-04 14:47:20 | DEBUG | 收到消息: {'MsgId': 1991030765, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "***********@chatroom" type="1" idbuffer="media:0_0" md5="466320cb744d1070b238d8bc5cf2b6ef" len = "9071" productid="" androidmd5="466320cb744d1070b238d8bc5cf2b6ef" androidlen="9071" s60v3md5 = "466320cb744d1070b238d8bc5cf2b6ef" s60v3len="9071" s60v5md5 = "466320cb744d1070b238d8bc5cf2b6ef" s60v5len="9071" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=466320cb744d1070b238d8bc5cf2b6ef&amp;filekey=3043020101042f302d02016e04025348042034363633323063623734346431303730623233386438626335636632623665660202236f040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032323035303530393032333730303061643662353163643036323139373236376234306230303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=c7998d9f687039a722f4b6be5a6022b3&amp;filekey=3043020101042f302d02016e040253480420633739393864396636383730333961373232663462366265356136303232623302022370040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032323035303530393032333730303063336265383163643036323139373236376234306230303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "4bee90c6b4194e5c876e1b4b68617dd3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=60fead08908d65f528edf94e104ce722&amp;filekey=3043020101042f302d02016e040253480420363066656164303839303864363566353238656466393465313034636537323202020490040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032323035303530393032333730303064303639613163643036323139373236376234306230303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "4b9002c53b4410c9200d3f169342a18f" width= "90" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290047, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_Nqhpr8bQ|v1_OCzFGi1C</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5809276730320493214, 'MsgSeq': 871424916}
2025-08-04 14:47:20 | INFO | 收到表情消息: 消息ID:1991030765 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 MD5:466320cb744d1070b238d8bc5cf2b6ef 大小:9071
2025-08-04 14:47:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5809276730320493214
2025-08-04 14:48:07 | DEBUG | 收到消息: {'MsgId': 1034628008, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg>\n\t\t<title>群聊的聊天记录</title>\n\t\t<des>阁主: [图片]\n阁主: [图片]\n阁主: 8月初 安卓会员版软件更新动态\n追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 \nTV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测\n酷我音乐会员版********  新增一个版本\n 酷我畅听会员版（修复无法听书问题）\n抠图软件  视频剪辑软件\n喜马拉雅极速版 番茄畅听 番茄小说 \n资源易和谐，建议尽快抽空下载体验，早下载早享受\n因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）\n后期根据大家实际需求在做调整\n追剧类软件请到追剧目录自取\n小说听书类在小说目录自取 音乐类在音乐目录自取\n其他在合集各目录自取\n移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。\n建议先转存资源到云盘，再下载，那样稳定一些\n各位粉丝有朋友想进群的，欢迎邀请进群。\n公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联\n移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0\n\n/*h9eAPqy5pvCi:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n\n移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c\n\n/*19eAPqjvP2yj:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl\n\n/*b9eAP7o1CicK:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n\n\n长截图  查看原图可看清晰图片 或朋友圈查阅\n本次更新软件会同步重置新链接 按软件性质从分类自取 约60款常用软件\n喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。\n\n@所有人\n阁主: [图片]\n阁主: 8月2号 安卓会员版软件更新动态\n酷我音乐********会员版（安柯二改）\n输入法会员版 囧次元 证件照制作软件 爱听书\n一刻相册 科学上网（粉丝需求）\n追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件\xa0 短剧软件\xa0 大师兄影视同步更新3.4.0纯净版版 \nTV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测\n抠图软件\xa0 视频剪辑软件\n喜马拉雅极速版 番茄畅听 番茄小说 \n资源易和谐，建议尽快抽空下载体验，早下载早享受\n\n因大部分粉丝习惯apk格式，按分类影视的资源同步apk.\xa0\xa0 日期版的同步压缩包\xa0 按你实际需求下载资源\n\n追剧类软件请到追剧目录自取\n小说听书类在小说目录自取 音乐类在音乐目录自取\n其他在合集各目录自取\n移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。\n建议先转存资源到云盘，再下载，那样稳定一些\n各位粉丝有朋友想进群的，欢迎邀请进群。\n公众号\xa0 QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联\n\n点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源\n移动云盘不限速资源链接1:\xa0 https://caiyun.139.com/w/i/2oRhbxZC0pPn0\n\n/*h9eAPqy5pvCi:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n\n移动云盘不限速资源链接2:\xa0 https://caiyun.139.com/w/i/2oRhbH0hLxH3c\n\n/*19eAPqjvP2yj:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n移动云盘不限速资源链接3:\xa0 https://caiyun.139.com/w/i/2oRhjeypPS7nl\n\n/*b9eAP7o1CicK:/ \n复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！\n\n\n长截图\xa0 查看原图可看清晰图片 或朋友圈查阅\n\n关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？\nhttps://tp.wjx.top/vm/mBrmuKy.aspx...</des>\n\t\t<action>view</action>\n\t\t<type>19</type>\n\t\t<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>\n\t\t<recorditem><![CDATA[<recordinfo><title>群聊的聊天记录</title><desc>阁主:&#x20;[图片]&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件\xa0&#x20;短剧软件\xa0&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件\xa0&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.\xa0\xa0&#x20;日期版的同步压缩包\xa0&#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号\xa0&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1:\xa0&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:\xa0&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:\xa0&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图\xa0&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx...</desc><datalist count="7"><dataitem datatype="2" dataid="80c06e7fec3826ffe446bf79de355de0"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042464336230333738362d666262612d346336302d616664332d6363383737393161303237610204059420010201000405004c55ce00</cdnthumburl><cdnthumbkey>8b242724528835e161375906b783450a</cdnthumbkey><thumbfullmd5>90851b83d75bdf1eaa492a9dbc585ea9</thumbfullmd5><thumbsize>5008</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057a9042465306262663635612d353934622d346532362d623061612d3036396330343137636135320204059420010201000405004c55ce00</cdndataurl><cdndatakey>adcf62011d4bab0c162bed7bd431dd01</cdndatakey><fullmd5>499938f4c6b6c8c354424462a3169579</fullmd5><datasize>2316118</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:29</sourcetime><srcMsgCreateTime>1754006249</srcMsgCreateTime><fromnewmsgid>3066609762073727088</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="c7ca7fe82f9fdbcf1b7a0cf0d61ae299"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042435633937316538642d353630632d343736642d383532342d6338376134643634363564640204059420010201000405004c4dfe00</cdnthumburl><cdnthumbkey>3a37235f4d40200edd77badccf42b1e9</cdnthumbkey><thumbfullmd5>79e5aff08d3f5ff976aed6535bebcabf</thumbfullmd5><thumbsize>4986</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042463663935663738342d663435332d343839342d386330352d6138343235343536383563620204059820010201000405004c505600</cdndataurl><cdndatakey>c7ad34d39bd4b4f2acee2f43a526a7b2</cdndatakey><fullmd5>9259e7715469f1ef1f2a5f0282ff79ec</fullmd5><datasize>2153307</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:31</sourcetime><srcMsgCreateTime>1754006251</srcMsgCreateTime><fromnewmsgid>1290166533093770947</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="fb925426627d3444a4d52df37bb3353b"><datadesc>8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:37</sourcetime><srcMsgCreateTime>1754006257</srcMsgCreateTime><fromnewmsgid>638303269258218996</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="8a99b560578c3e20440539f3785b4231"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042464643932643566612d346439612d343761632d386330642d6132633835633163383637340204059420010201000405004c511e00</cdnthumburl><cdnthumbkey>fb63a9c47dab0f1d8813e314948c9638</cdnthumbkey><thumbfullmd5>896e575357b122d1d4489638517f2b9f</thumbfullmd5><thumbsize>4816</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042462303265313364622d636362642d343337332d393039342d3366343839623939663534660204059420010201000405004c4d9a00</cdndataurl><cdndatakey>a84f3ba8211c7b408cf6dd21a374a91e</cdndatakey><fullmd5>19f4cc8ee492641ec57f800c6cfa00e0</fullmd5><datasize>1750258</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:46:59</sourcetime><srcMsgCreateTime>1754128019</srcMsgCreateTime><fromnewmsgid>7960802104766101416</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="2f135ae9bc673a251aef10bb9bfa93e0"><datadesc>8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件\xa0&#x20;短剧软件\xa0&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件\xa0&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.\xa0\xa0&#x20;日期版的同步压缩包\xa0&#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号\xa0&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1:\xa0&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:\xa0&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:\xa0&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图\xa0&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:47:07</sourcetime><srcMsgCreateTime>1754128027</srcMsgCreateTime><fromnewmsgid>8974754232599990384</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="ee84cd66dafd0d24e67b14f296ffb659"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ae042434643539646539342d356565372d346461352d393537322d3461383634356233383065350204059820010201000405004c4d3600</cdnthumburl><cdnthumbkey>6889d3eeebae356d998dd4f810977cbc</cdnthumbkey><thumbfullmd5>88076d47343952f322e7851ff4993987</thumbfullmd5><thumbsize>4820</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042431373831633564332d643561632d346533302d616664382d3164626130326663633261380204059820010201000405004c537600</cdndataurl><cdndatakey>0a897c80fdde36cfc2de35d9426d081c</cdndatakey><fullmd5>f0580f479d0b339c822f695e271ae409</fullmd5><datasize>1420786</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:56</sourcetime><srcMsgCreateTime>1754218616</srcMsgCreateTime><fromnewmsgid>116176850563573712</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="7795b80485b888e6850aff63a9c42cfb"><datadesc>8月3号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********-********版&#x0A;百词斩&#x20;欧路词典&#x20;河马剧场&#x20;泰剧&#x20;彩云天气&#x20;墨迹天气&#x20;格式工厂&#x20;万能格式转化等&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版（必须登录&#x20;若不能用就更换登录号码）&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类整理的资源更新apk格式.&#x20;&#x20;&#x20;&#x0A;日期版的更新的资源，更新压缩包&#x20;&#x20;&#x0A;按你实际需求下载资源&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他软件基本在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;具体更新明细，查阅长截图&#x0A;&#x20;查看原图可看清晰图片&#x20;或朋友圈自取</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:59</sourcetime><srcMsgCreateTime>1754218619</srcMsgCreateTime><fromnewmsgid>3415431080107857033</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem></datalist><isChatRoom>1</isChatRoom></recordinfo>]]></recorditem>\n\t\t<appattach />\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290094, 'MsgSource': '<msgsource>\n\t<passthrough>\n\t\t<forward_depth>0</forward_depth>\n\t</passthrough>\n\t<sec_msg_node>\n\t\t<uuid>13c0cfc5720aa5ae54c3dcd9b8e8ac35_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_xED8zpQl|v1_cnvQkPqR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : [聊天记录]', 'NewMsgId': 2571039548125235929, 'MsgSeq': 871424917}
2025-08-04 14:48:07 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-04 14:48:07 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>群聊的聊天记录</title>
		<des>阁主: [图片]
阁主: [图片]
阁主: 8月初 安卓会员版软件更新动态
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
酷我音乐会员版********  新增一个版本
 酷我畅听会员版（修复无法听书问题）
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受
因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）
后期根据大家实际需求在做调整
追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅
本次更新软件会同步重置新链接 按软件性质从分类自取 约60款常用软件
喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。

@所有人
阁主: [图片]
阁主: 8月2号 安卓会员版软件更新动态
酷我音乐********会员版（安柯二改）
输入法会员版 囧次元 证件照制作软件 爱听书
一刻相册 科学上网（粉丝需求）
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受

因大部分粉丝习惯apk格式，按分类影视的资源同步apk.   日期版的同步压缩包  按你实际需求下载资源

追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联

点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅

关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？
https://tp.wjx.top/vm/mBrmuKy.aspx...</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>群聊的聊天记录</title><desc>阁主:&#x20;[图片]&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件 &#x20;短剧软件 &#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件 &#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.  &#x20;日期版的同步压缩包 &#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号 &#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1: &#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2: &#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3: &#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图 &#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx...</desc><datalist count="7"><dataitem datatype="2" dataid="80c06e7fec3826ffe446bf79de355de0"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042464336230333738362d666262612d346336302d616664332d6363383737393161303237610204059420010201000405004c55ce00</cdnthumburl><cdnthumbkey>8b242724528835e161375906b783450a</cdnthumbkey><thumbfullmd5>90851b83d75bdf1eaa492a9dbc585ea9</thumbfullmd5><thumbsize>5008</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057a9042465306262663635612d353934622d346532362d623061612d3036396330343137636135320204059420010201000405004c55ce00</cdndataurl><cdndatakey>adcf62011d4bab0c162bed7bd431dd01</cdndatakey><fullmd5>499938f4c6b6c8c354424462a3169579</fullmd5><datasize>2316118</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:29</sourcetime><srcMsgCreateTime>1754006249</srcMsgCreateTime><fromnewmsgid>3066609762073727088</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="c7ca7fe82f9fdbcf1b7a0cf0d61ae299"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042435633937316538642d353630632d343736642d383532342d6338376134643634363564640204059420010201000405004c4dfe00</cdnthumburl><cdnthumbkey>3a37235f4d40200edd77badccf42b1e9</cdnthumbkey><thumbfullmd5>79e5aff08d3f5ff976aed6535bebcabf</thumbfullmd5><thumbsize>4986</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042463663935663738342d663435332d343839342d386330352d6138343235343536383563620204059820010201000405004c505600</cdndataurl><cdndatakey>c7ad34d39bd4b4f2acee2f43a526a7b2</cdndatakey><fullmd5>9259e7715469f1ef1f2a5f0282ff79ec</fullmd5><datasize>2153307</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:31</sourcetime><srcMsgCreateTime>1754006251</srcMsgCreateTime><fromnewmsgid>1290166533093770947</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="fb925426627d3444a4d52df37bb3353b"><datadesc>8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:37</sourcetime><srcMsgCreateTime>1754006257</srcMsgCreateTime><fromnewmsgid>638303269258218996</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="8a99b560578c3e20440539f3785b4231"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042464643932643566612d346439612d343761632d386330642d6132633835633163383637340204059420010201000405004c511e00</cdnthumburl><cdnthumbkey>fb63a9c47dab0f1d8813e314948c9638</cdnthumbkey><thumbfullmd5>896e575357b122d1d4489638517f2b9f</thumbfullmd5><thumbsize>4816</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042462303265313364622d636362642d343337332d393039342d3366343839623939663534660204059420010201000405004c4d9a00</cdndataurl><cdndatakey>a84f3ba8211c7b408cf6dd21a374a91e</cdndatakey><fullmd5>19f4cc8ee492641ec57f800c6cfa00e0</fullmd5><datasize>1750258</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:46:59</sourcetime><srcMsgCreateTime>1754128019</srcMsgCreateTime><fromnewmsgid>7960802104766101416</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="2f135ae9bc673a251aef10bb9bfa93e0"><datadesc>8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件 &#x20;短剧软件 &#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件 &#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.  &#x20;日期版的同步压缩包 &#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号 &#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1: &#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2: &#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3: &#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图 &#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:47:07</sourcetime><srcMsgCreateTime>1754128027</srcMsgCreateTime><fromnewmsgid>8974754232599990384</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="ee84cd66dafd0d24e67b14f296ffb659"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ae042434643539646539342d356565372d346461352d393537322d3461383634356233383065350204059820010201000405004c4d3600</cdnthumburl><cdnthumbkey>6889d3eeebae356d998dd4f810977cbc</cdnthumbkey><thumbfullmd5>88076d47343952f322e7851ff4993987</thumbfullmd5><thumbsize>4820</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042431373831633564332d643561632d346533302d616664382d3164626130326663633261380204059820010201000405004c537600</cdndataurl><cdndatakey>0a897c80fdde36cfc2de35d9426d081c</cdndatakey><fullmd5>f0580f479d0b339c822f695e271ae409</fullmd5><datasize>1420786</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:56</sourcetime><srcMsgCreateTime>1754218616</srcMsgCreateTime><fromnewmsgid>116176850563573712</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="7795b80485b888e6850aff63a9c42cfb"><datadesc>8月3号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********-********版&#x0A;百词斩&#x20;欧路词典&#x20;河马剧场&#x20;泰剧&#x20;彩云天气&#x20;墨迹天气&#x20;格式工厂&#x20;万能格式转化等&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版（必须登录&#x20;若不能用就更换登录号码）&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类整理的资源更新apk格式.&#x20;&#x20;&#x20;&#x0A;日期版的更新的资源，更新压缩包&#x20;&#x20;&#x0A;按你实际需求下载资源&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他软件基本在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;具体更新明细，查阅长截图&#x0A;&#x20;查看原图可看清晰图片&#x20;或朋友圈自取</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:59</sourcetime><srcMsgCreateTime>1754218619</srcMsgCreateTime><fromnewmsgid>3415431080107857033</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem></datalist><isChatRoom>1</isChatRoom></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-04 14:48:07 | DEBUG | XML消息类型: 19
2025-08-04 14:48:07 | DEBUG | XML消息标题: 群聊的聊天记录
2025-08-04 14:48:07 | DEBUG | XML消息描述: 阁主: [图片]
阁主: [图片]
阁主: 8月初 安卓会员版软件更新动态
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
酷我音乐会员版********  新增一个版本
 酷我畅听会员版（修复无法听书问题）
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受
因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）
后期根据大家实际需求在做调整
追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅
本次更新软件会同步重置新链接 按软件性质从分类自取 约60款常用软件
喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。

@所有人
阁主: [图片]
阁主: 8月2号 安卓会员版软件更新动态
酷我音乐********会员版（安柯二改）
输入法会员版 囧次元 证件照制作软件 爱听书
一刻相册 科学上网（粉丝需求）
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受

因大部分粉丝习惯apk格式，按分类影视的资源同步apk.   日期版的同步压缩包  按你实际需求下载资源

追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联

点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅

关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？
https://tp.wjx.top/vm/mBrmuKy.aspx...
2025-08-04 14:48:07 | DEBUG | XML消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-08-04 14:48:07 | INFO | 未知的XML消息类型: 19
2025-08-04 14:48:07 | INFO | 消息标题: 群聊的聊天记录
2025-08-04 14:48:07 | INFO | 消息描述: 阁主: [图片]
阁主: [图片]
阁主: 8月初 安卓会员版软件更新动态
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
酷我音乐会员版********  新增一个版本
 酷我畅听会员版（修复无法听书问题）
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受
因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）
后期根据大家实际需求在做调整
追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅
本次更新软件会同步重置新链接 按软件性质从分类自取 约60款常用软件
喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。

@所有人
阁主: [图片]
阁主: 8月2号 安卓会员版软件更新动态
酷我音乐********会员版（安柯二改）
输入法会员版 囧次元 证件照制作软件 爱听书
一刻相册 科学上网（粉丝需求）
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受

因大部分粉丝习惯apk格式，按分类影视的资源同步apk.   日期版的同步压缩包  按你实际需求下载资源

追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联

点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅

关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？
https://tp.wjx.top/vm/mBrmuKy.aspx...
2025-08-04 14:48:07 | INFO | 消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-08-04 14:48:07 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>群聊的聊天记录</title>
		<des>阁主: [图片]
阁主: [图片]
阁主: 8月初 安卓会员版软件更新动态
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
酷我音乐会员版********  新增一个版本
 酷我畅听会员版（修复无法听书问题）
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受
因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）
后期根据大家实际需求在做调整
追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅
本次更新软件会同步重置新链接 按软件性质从分类自取 约60款常用软件
喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。

@所有人
阁主: [图片]
阁主: 8月2号 安卓会员版软件更新动态
酷我音乐********会员版（安柯二改）
输入法会员版 囧次元 证件照制作软件 爱听书
一刻相册 科学上网（粉丝需求）
追剧软件（热门电影电视剧免费看） 漫画软件 剪辑软件 学习软件 小说听书软件  短剧软件  大师兄影视同步更新3.4.0纯净版版 
TV版横屏追剧软件 电视版 具体是否支持你家盒子需要自测
抠图软件  视频剪辑软件
喜马拉雅极速版 番茄畅听 番茄小说 
资源易和谐，建议尽快抽空下载体验，早下载早享受

因大部分粉丝习惯apk格式，按分类影视的资源同步apk.   日期版的同步压缩包  按你实际需求下载资源

追剧类软件请到追剧目录自取
小说听书类在小说目录自取 音乐类在音乐目录自取
其他在合集各目录自取
移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。
建议先转存资源到云盘，再下载，那样稳定一些
各位粉丝有朋友想进群的，欢迎邀请进群。
公众号  QQ频道 朋友圈 微信群同步更新资源 各位都可以加一下，避免失联

点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源
移动云盘不限速资源链接1:  https://caiyun.139.com/w/i/2oRhbxZC0pPn0

/*h9eAPqy5pvCi:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！

移动云盘不限速资源链接2:  https://caiyun.139.com/w/i/2oRhbH0hLxH3c

/*19eAPqjvP2yj:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！
移动云盘不限速资源链接3:  https://caiyun.139.com/w/i/2oRhjeypPS7nl

/*b9eAP7o1CicK:/ 
复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！


长截图  查看原图可看清晰图片 或朋友圈查阅

关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？
https://tp.wjx.top/vm/mBrmuKy.aspx...</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>群聊的聊天记录</title><desc>阁主:&#x20;[图片]&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人&#x0A;阁主:&#x20;[图片]&#x0A;阁主:&#x20;8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件 &#x20;短剧软件 &#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件 &#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.  &#x20;日期版的同步压缩包 &#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号 &#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1: &#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2: &#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3: &#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图 &#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx...</desc><datalist count="7"><dataitem datatype="2" dataid="80c06e7fec3826ffe446bf79de355de0"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042464336230333738362d666262612d346336302d616664332d6363383737393161303237610204059420010201000405004c55ce00</cdnthumburl><cdnthumbkey>8b242724528835e161375906b783450a</cdnthumbkey><thumbfullmd5>90851b83d75bdf1eaa492a9dbc585ea9</thumbfullmd5><thumbsize>5008</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057a9042465306262663635612d353934622d346532362d623061612d3036396330343137636135320204059420010201000405004c55ce00</cdndataurl><cdndatakey>adcf62011d4bab0c162bed7bd431dd01</cdndatakey><fullmd5>499938f4c6b6c8c354424462a3169579</fullmd5><datasize>2316118</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:29</sourcetime><srcMsgCreateTime>1754006249</srcMsgCreateTime><fromnewmsgid>3066609762073727088</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="c7ca7fe82f9fdbcf1b7a0cf0d61ae299"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042435633937316538642d353630632d343736642d383532342d6338376134643634363564640204059420010201000405004c4dfe00</cdnthumburl><cdnthumbkey>3a37235f4d40200edd77badccf42b1e9</cdnthumbkey><thumbfullmd5>79e5aff08d3f5ff976aed6535bebcabf</thumbfullmd5><thumbsize>4986</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057aa042463663935663738342d663435332d343839342d386330352d6138343235343536383563620204059820010201000405004c505600</cdndataurl><cdndatakey>c7ad34d39bd4b4f2acee2f43a526a7b2</cdndatakey><fullmd5>9259e7715469f1ef1f2a5f0282ff79ec</fullmd5><datasize>2153307</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:31</sourcetime><srcMsgCreateTime>1754006251</srcMsgCreateTime><fromnewmsgid>1290166533093770947</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="fb925426627d3444a4d52df37bb3353b"><datadesc>8月初&#x20;安卓会员版软件更新动态&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;酷我音乐会员版********&#x20;&#x20;新增一个版本&#x0A;&#x20;酷我畅听会员版（修复无法听书问题）&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;因部分群友反馈apk格式下载时因病毒扫描存在限速问题，并且apk安装包容易和谐失效，因此自7.27日安装包改为zip格式压缩包分享，下载后手机文件管理中解压后安装。（开了云盘会员的可以云端解压，没会员的下载后本地解压）&#x0A;后期根据大家实际需求在做调整&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图&#x20;&#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;本次更新软件会同步重置新链接&#x20;按软件性质从分类自取&#x20;约60款常用软件&#x0A;喜欢玩免流的粉丝，小编朋友圈自取云免软件测试，测试可用么自己找软件内客服买月卡使用，移动联通部分卡友可免。&#x0A;&#x0A;@所有人</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-01&#x20;07:57:37</sourcetime><srcMsgCreateTime>1754006257</srcMsgCreateTime><fromnewmsgid>638303269258218996</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="8a99b560578c3e20440539f3785b4231"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042464643932643566612d346439612d343761632d386330642d6132633835633163383637340204059420010201000405004c511e00</cdnthumburl><cdnthumbkey>fb63a9c47dab0f1d8813e314948c9638</cdnthumbkey><thumbfullmd5>896e575357b122d1d4489638517f2b9f</thumbfullmd5><thumbsize>4816</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ac042462303265313364622d636362642d343337332d393039342d3366343839623939663534660204059420010201000405004c4d9a00</cdndataurl><cdndatakey>a84f3ba8211c7b408cf6dd21a374a91e</cdndatakey><fullmd5>19f4cc8ee492641ec57f800c6cfa00e0</fullmd5><datasize>1750258</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:46:59</sourcetime><srcMsgCreateTime>1754128019</srcMsgCreateTime><fromnewmsgid>7960802104766101416</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="2f135ae9bc673a251aef10bb9bfa93e0"><datadesc>8月2号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********会员版（安柯二改）&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件 &#x20;短剧软件 &#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件 &#x20;视频剪辑软件&#x0A;喜马拉雅极速版&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类影视的资源同步apk.  &#x20;日期版的同步压缩包 &#x20;按你实际需求下载资源&#x0A;&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号 &#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;移动云盘不限速资源链接1: &#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接2: &#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;移动云盘不限速资源链接3: &#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;&#x0A;长截图 &#x20;查看原图可看清晰图片&#x20;或朋友圈查阅&#x0A;&#x0A;关于资源更新格式的调研，各位群友实际使用下，apk格式的安装包，还是zip压缩包下载快呢？&#x0A;https://tp.wjx.top/vm/mBrmuKy.aspx</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-02&#x20;17:47:07</sourcetime><srcMsgCreateTime>1754128027</srcMsgCreateTime><fromnewmsgid>8974754232599990384</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="2" dataid="ee84cd66dafd0d24e67b14f296ffb659"><datadesc>[图片]</datadesc><cdnthumburl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ae042434643539646539342d356565372d346461352d393537322d3461383634356233383065350204059820010201000405004c4d3600</cdnthumburl><cdnthumbkey>6889d3eeebae356d998dd4f810977cbc</cdnthumbkey><thumbfullmd5>88076d47343952f322e7851ff4993987</thumbfullmd5><thumbsize>4820</thumbsize><cdndataurl>************4b3049020100020468cde53a02032f4f560204677ac2dc0204689057ad042431373831633564332d643561632d346533302d616664382d3164626130326663633261380204059820010201000405004c537600</cdndataurl><cdndatakey>0a897c80fdde36cfc2de35d9426d081c</cdndatakey><fullmd5>f0580f479d0b339c822f695e271ae409</fullmd5><datasize>1420786</datasize><thumbwidth>58</thumbwidth><thumbheight>144</thumbheight><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:56</sourcetime><srcMsgCreateTime>1754218616</srcMsgCreateTime><fromnewmsgid>116176850563573712</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="7795b80485b888e6850aff63a9c42cfb"><datadesc>8月3号&#x20;安卓会员版软件更新动态&#x0A;酷我音乐********-********版&#x0A;百词斩&#x20;欧路词典&#x20;河马剧场&#x20;泰剧&#x20;彩云天气&#x20;墨迹天气&#x20;格式工厂&#x20;万能格式转化等&#x0A;输入法会员版&#x20;囧次元&#x20;证件照制作软件&#x20;爱听书&#x0A;一刻相册&#x20;科学上网（粉丝需求）&#x0A;追剧软件（热门电影电视剧免费看）&#x20;漫画软件&#x20;剪辑软件&#x20;学习软件&#x20;小说听书软件&#x20;&#x20;短剧软件&#x20;&#x20;大师兄影视同步更新3.4.0纯净版版&#x20;&#x0A;TV版横屏追剧软件&#x20;电视版&#x20;具体是否支持你家盒子需要自测&#x0A;抠图软件&#x20;&#x20;视频剪辑软件&#x0A;喜马拉雅极速版（必须登录&#x20;若不能用就更换登录号码）&#x20;番茄畅听&#x20;番茄小说&#x20;&#x0A;资源易和谐，建议尽快抽空下载体验，早下载早享受&#x0A;&#x0A;因大部分粉丝习惯apk格式，按分类整理的资源更新apk格式.&#x20;&#x20;&#x20;&#x0A;日期版的更新的资源，更新压缩包&#x20;&#x20;&#x0A;按你实际需求下载资源&#x0A;追剧类软件请到追剧目录自取&#x0A;小说听书类在小说目录自取&#x20;音乐类在音乐目录自取&#x0A;其他软件基本在合集各目录自取&#x0A;移动联通电信号码都能下载注册使用移动云盘不限速缓存资源，若下载慢，可以暂停，退出软件重新进去。下载过程建议停留在云盘客户端内。&#x0A;建议先转存资源到云盘，再下载，那样稳定一些&#x0A;各位粉丝有朋友想进群的，欢迎邀请进群。&#x0A;公众号&#x20;&#x20;QQ频道&#x20;朋友圈&#x20;微信群同步更新资源&#x20;各位都可以加一下，避免失联&#x0A;&#x0A;点击下面任意一个链接跳转云盘下载资源，或者单独复制某个链接打开云盘获取资源&#x0A;&#x0A;移动云盘不限速资源链接2:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbH0hLxH3c&#x0A;&#x0A;/*19eAPqjvP2yj:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接1:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhbxZC0pPn0&#x0A;&#x0A;/*h9eAPqy5pvCi:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;&#x0A;移动云盘不限速资源链接3:&#x20;&#x20;https://caiyun.139.com/w/i/2oRhjeypPS7nl&#x0A;&#x0A;/*b9eAP7o1CicK:/&#x20;&#x0A;复制本段信息，打开「中国移动云盘APP」获取。人人不限速，移动用户免流量！&#x0A;具体更新明细，查阅长截图&#x0A;&#x20;查看原图可看清晰图片&#x20;或朋友圈自取</datadesc><sourcename>阁主</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/baFdIna5ckHoog6e1k0r69eMR14anEIP3VBkUY2oQibNEjRrFMaX2jR2B5lBlm6Jj7zho7BBOCLrQuAp6UVibrjzLWLQuN9nzXSDibhMWVy4ckMoKdkw0n1YbBU0S0wWuk0uWG8UhjIF0wlQ4j8aIicdvg/96</sourceheadurl><sourcetime>2025-08-03&#x20;18:56:59</sourcetime><srcMsgCreateTime>1754218619</srcMsgCreateTime><fromnewmsgid>3415431080107857033</fromnewmsgid><dataitemsource><hashusername>32f85ed91769293cf3fef7bcf82dd210cc5c156e4e1c4fee9ab1686a828e8940</hashusername></dataitemsource></dataitem></datalist><isChatRoom>1</isChatRoom></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-04 14:48:26 | DEBUG | 收到消息: {'MsgId': 635923829, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n安卓的自己找吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290114, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_/Q3UXmtC|v1_bjrcgE8c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 安卓的自己找吧', 'NewMsgId': 3689126987749335543, 'MsgSeq': 871424918}
2025-08-04 14:48:26 | INFO | 收到文本消息: 消息ID:635923829 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:安卓的自己找吧
2025-08-04 14:48:27 | DEBUG | [DouBaoImageToImage] 收到文本消息: '安卓的自己找吧' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:48:27 | DEBUG | [DouBaoImageToImage] 命令解析: ['安卓的自己找吧']
2025-08-04 14:48:27 | DEBUG | 处理消息内容: '安卓的自己找吧'
2025-08-04 14:48:27 | DEBUG | 消息内容 '安卓的自己找吧' 不匹配任何命令，忽略
2025-08-04 14:48:39 | DEBUG | 收到消息: {'MsgId': 716500191, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n我看看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290126, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_NDLxQFoN|v1_aSjDWFfu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4416092657915925004, 'MsgSeq': 871424919}
2025-08-04 14:48:39 | INFO | 收到文本消息: 消息ID:716500191 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:我看看
2025-08-04 14:48:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我看看' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:48:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['我看看']
2025-08-04 14:48:39 | DEBUG | 处理消息内容: '我看看'
2025-08-04 14:48:39 | DEBUG | 消息内容 '我看看' 不匹配任何命令，忽略
2025-08-04 14:48:42 | DEBUG | 收到消息: {'MsgId': 1654785906, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n@知鱼ˇ\u2005有那么大难度，我那天蹦跶几下就上去了[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290127, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_8kgajq8ks1nv22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_nbtl6c3y|v1_+WFAHVps</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3432610669527387983, 'MsgSeq': 871424920}
2025-08-04 14:48:42 | INFO | 收到文本消息: 消息ID:1654785906 来自:***********@chatroom 发送人:wangchunmeng7291 @:['wxid_8kgajq8ks1nv22'] 内容:@知鱼ˇ 有那么大难度，我那天蹦跶几下就上去了[捂脸]
2025-08-04 14:48:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@知鱼ˇ 有那么大难度，我那天蹦跶几下就上去了[捂脸]' from wangchunmeng7291 in ***********@chatroom
2025-08-04 14:48:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['@知鱼ˇ\u2005有那么大难度，我那天蹦跶几下就上去了[捂脸]']
2025-08-04 14:48:42 | DEBUG | 处理消息内容: '@知鱼ˇ 有那么大难度，我那天蹦跶几下就上去了[捂脸]'
2025-08-04 14:48:42 | DEBUG | 消息内容 '@知鱼ˇ 有那么大难度，我那天蹦跶几下就上去了[捂脸]' 不匹配任何命令，忽略
2025-08-04 14:48:53 | DEBUG | 收到消息: {'MsgId': 1692589558, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我现在手机上没安装，有一个视频软件那个手机在家里没带'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290140, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_d5lqv7v7|v1_okODkRMH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 我现在手机上没安装，有一个视频软件那个手机在家里没带', 'NewMsgId': 1698319798628254369, 'MsgSeq': 871424921}
2025-08-04 14:48:53 | INFO | 收到文本消息: 消息ID:1692589558 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我现在手机上没安装，有一个视频软件那个手机在家里没带
2025-08-04 14:48:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我现在手机上没安装，有一个视频软件那个手机在家里没带' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:48:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['我现在手机上没安装，有一个视频软件那个手机在家里没带']
2025-08-04 14:48:53 | DEBUG | 处理消息内容: '我现在手机上没安装，有一个视频软件那个手机在家里没带'
2025-08-04 14:48:53 | DEBUG | 消息内容 '我现在手机上没安装，有一个视频软件那个手机在家里没带' 不匹配任何命令，忽略
2025-08-04 14:48:56 | DEBUG | 收到消息: {'MsgId': 254258335, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ujr4wq0ymfjn22:\n唱跳rap'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290139, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_tnAVA88P|v1_g/uQ7q1y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '程zzz\ue10b : 唱跳rap', 'NewMsgId': 212661651492206930, 'MsgSeq': 871424922}
2025-08-04 14:48:56 | INFO | 收到文本消息: 消息ID:254258335 来自:***********@chatroom 发送人:wxid_ujr4wq0ymfjn22 @:[] 内容:唱跳rap
2025-08-04 14:48:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '唱跳rap' from wxid_ujr4wq0ymfjn22 in ***********@chatroom
2025-08-04 14:48:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['唱跳rap']
2025-08-04 14:48:56 | DEBUG | 处理消息内容: '唱跳rap'
2025-08-04 14:48:56 | DEBUG | 消息内容 '唱跳rap' 不匹配任何命令，忽略
2025-08-04 14:49:04 | DEBUG | 收到消息: {'MsgId': 1211152729, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10000, 'Content': {'string': '"昭禾以纯"已启用“群聊邀请确认”，群成员需群主或群管理员确认才能邀请朋友进群。'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290149, 'MsgSource': '<msgsource>\n\t<signature>v1_adrT52r3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 419058146208699969, 'MsgSeq': 871424923}
2025-08-04 14:49:04 | INFO | 未知的消息类型: {'MsgId': 1211152729, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10000, 'Content': {'string': '"昭禾以纯"已启用“群聊邀请确认”，群成员需群主或群管理员确认才能邀请朋友进群。'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290149, 'MsgSource': '<msgsource>\n\t<signature>v1_adrT52r3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 419058146208699969, 'MsgSeq': 871424923, 'FromWxid': '***********@chatroom'}
2025-08-04 14:49:12 | DEBUG | 收到消息: {'MsgId': 1208430802, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这不马上就登顶了吗</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>6216487877801470913</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_8kgajq8ks1nv22</chatusr>\n\t\t\t<displayname>知鱼ˇ</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6ac5b451c24bcedf304d35e123ebba67" encryver="1" cdnthumbaeskey="6ac5b451c24bcedf304d35e123ebba67" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" cdnthumblength="4366" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" length="145655" md5="cfb7b1a51c1f175ab5de111fe4a58361" hevc_mid_size="145655" originsourcemd5="27b7c0957b23d29108e70ec7825a603f"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImY1OTEwMDAwNDAwMDAwMDAiLCJwZHFoYXNoIjoiMzNjMzAzZTM1N2U4ZTM2M2E3NTg5ZDM2MmFiZDBjYmUyYWZjYjgwMzNmNjJmMDIzMTVmMmY0MWJlODE2ODI1YyJ9&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853095636&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;77a229cb69a17752381e7d07fac7e45d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="145655" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;150&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_pvQUNoCp|v1_8SIBW8Nd&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754290008</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_5kipwrzramxr22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290159, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3f0ce14dbb6fef9335dc92e80189738b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_PzqnByXI|v1_wZDqqUHt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2724886349012105089, 'MsgSeq': 871424924}
2025-08-04 14:49:12 | DEBUG | 从群聊消息中提取发送者: wxid_5kipwrzramxr22
2025-08-04 14:49:12 | DEBUG | 使用已解析的XML处理引用消息
2025-08-04 14:49:12 | INFO | 收到引用消息: 消息ID:1208430802 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 内容:这不马上就登顶了吗 引用类型:3
2025-08-04 14:49:12 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-04 14:49:12 | INFO | [DouBaoImageToImage] 消息内容: '这不马上就登顶了吗' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 14:49:12 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['这不马上就登顶了吗']
2025-08-04 14:49:12 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-04 14:49:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-04 14:49:12 | INFO |   - 消息内容: 这不马上就登顶了吗
2025-08-04 14:49:12 | INFO |   - 群组ID: ***********@chatroom
2025-08-04 14:49:12 | INFO |   - 发送人: wxid_5kipwrzramxr22
2025-08-04 14:49:12 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这不马上就登顶了吗</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>6216487877801470913</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_8kgajq8ks1nv22</chatusr>\n\t\t\t<displayname>知鱼ˇ</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6ac5b451c24bcedf304d35e123ebba67" encryver="1" cdnthumbaeskey="6ac5b451c24bcedf304d35e123ebba67" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" cdnthumblength="4366" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" length="145655" md5="cfb7b1a51c1f175ab5de111fe4a58361" hevc_mid_size="145655" originsourcemd5="27b7c0957b23d29108e70ec7825a603f"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImY1OTEwMDAwNDAwMDAwMDAiLCJwZHFoYXNoIjoiMzNjMzAzZTM1N2U4ZTM2M2E3NTg5ZDM2MmFiZDBjYmUyYWZjYjgwMzNmNjJmMDIzMTVmMmY0MWJlODE2ODI1YyJ9&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853095636&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;77a229cb69a17752381e7d07fac7e45d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="145655" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;150&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_pvQUNoCp|v1_8SIBW8Nd&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754290008</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_5kipwrzramxr22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '6216487877801470913', 'NewMsgId': '6216487877801470913', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '知鱼ˇ', 'MsgSource': '<msgsource><sequence_id>853095636</sequence_id>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>77a229cb69a17752381e7d07fac7e45d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="145655" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_pvQUNoCp|v1_8SIBW8Nd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754290008', 'SenderWxid': 'wxid_5kipwrzramxr22'}
2025-08-04 14:49:12 | INFO |   - 引用消息ID: 
2025-08-04 14:49:12 | INFO |   - 引用消息类型: 
2025-08-04 14:49:12 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>这不马上就登顶了吗</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>6216487877801470913</svrid>
			<fromusr>***********@chatroom</fromusr>
			<chatusr>wxid_8kgajq8ks1nv22</chatusr>
			<displayname>知鱼ˇ</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="6ac5b451c24bcedf304d35e123ebba67" encryver="1" cdnthumbaeskey="6ac5b451c24bcedf304d35e123ebba67" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" cdnthumblength="4366" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02044761962402046890573b042465393866646662642d383536662d343535352d616262662d393239303766636133336232020405290a020201000405004c54a300" length="145655" md5="cfb7b1a51c1f175ab5de111fe4a58361" hevc_mid_size="145655" originsourcemd5="27b7c0957b23d29108e70ec7825a603f"&gt;
		&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImY1OTEwMDAwNDAwMDAwMDAiLCJwZHFoYXNoIjoiMzNjMzAzZTM1N2U4ZTM2M2E3NTg5ZDM2MmFiZDBjYmUyYWZjYjgwMzNmNjJmMDIzMTVmMmY0MWJlODE2ODI1YyJ9&lt;/secHashInfoBase64&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853095636&lt;/sequence_id&gt;
	&lt;bizflag&gt;0&lt;/bizflag&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;77a229cb69a17752381e7d07fac7e45d_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="145655" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;150&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_pvQUNoCp|v1_8SIBW8Nd&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1754290008</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_5kipwrzramxr22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-04 14:49:12 | INFO |   - 引用消息发送人: wxid_5kipwrzramxr22
2025-08-04 14:49:34 | DEBUG | 收到消息: {'MsgId': 298549600, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="407d2b652c226adb66e23f85e0768585" encryver="1" cdnthumbaeskey="407d2b652c226adb66e23f85e0768585" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020447619624020468905805042438363239613836372d663566642d343136392d396333392d316337613863376561636231020405250a020201000405004c51e700" cdnthumblength="4630" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020447619624020468905805042438363239613836372d663566642d343136392d396333392d316337613863376561636231020405250a020201000405004c51e700" length="208806" md5="77e2c950982fb443a63cda007d890d61" hevc_mid_size="208806" originsourcemd5="ff5f632a54af319dfe6530c4b8a5222d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY0NzUwMDEwMDAyMDQwMDAiLCJwZHFoYXNoIjoiNzFjY2ViNTQ0ZmE4Yzg1NjBmODBhNWYyYjZjODk5ZjJmNzA5ZTgyZjFlNTM5ZWQzMDBmMmM1YThiODc2MzM0ZSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290181, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>4bc26d18d89519dbe491752f5e7e5b54_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_MhjS4Vvg|v1_jnrljqv3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7602685150383109701, 'MsgSeq': 871424925}
2025-08-04 14:49:34 | INFO | 收到图片消息: 消息ID:298549600 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 XML:<?xml version="1.0"?><msg><img aeskey="407d2b652c226adb66e23f85e0768585" encryver="1" cdnthumbaeskey="407d2b652c226adb66e23f85e0768585" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020447619624020468905805042438363239613836372d663566642d343136392d396333392d316337613863376561636231020405250a020201000405004c51e700" cdnthumblength="4630" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020447619624020468905805042438363239613836372d663566642d343136392d396333392d316337613863376561636231020405250a020201000405004c51e700" length="208806" md5="77e2c950982fb443a63cda007d890d61" hevc_mid_size="208806" originsourcemd5="ff5f632a54af319dfe6530c4b8a5222d"><secHashInfoBase64>eyJwaGFzaCI6ImY0NzUwMDEwMDAyMDQwMDAiLCJwZHFoYXNoIjoiNzFjY2ViNTQ0ZmE4Yzg1NjBmODBhNWYyYjZjODk5ZjJmNzA5ZTgyZjFlNTM5ZWQzMDBmMmM1YThiODc2MzM0ZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:49:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:49:34 | INFO | [TimerTask] 缓存图片消息: 298549600
2025-08-04 14:49:38 | DEBUG | 收到消息: {'MsgId': 1620310643, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n就在这'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290185, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_jxT8UEzE|v1_Y0QVAxOE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3148012919968484148, 'MsgSeq': 871424926}
2025-08-04 14:49:38 | INFO | 收到文本消息: 消息ID:1620310643 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:就在这
2025-08-04 14:49:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就在这' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:49:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['就在这']
2025-08-04 14:49:38 | DEBUG | 处理消息内容: '就在这'
2025-08-04 14:49:38 | DEBUG | 消息内容 '就在这' 不匹配任何命令，忽略
2025-08-04 14:49:45 | DEBUG | 收到消息: {'MsgId': 1786012242, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n跳得我头疼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290192, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_2/XIvkOV|v1_JKDBJ0hk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8389538510383972558, 'MsgSeq': 871424927}
2025-08-04 14:49:45 | INFO | 收到文本消息: 消息ID:1786012242 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:跳得我头疼
2025-08-04 14:49:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '跳得我头疼' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:49:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['跳得我头疼']
2025-08-04 14:49:45 | DEBUG | 处理消息内容: '跳得我头疼'
2025-08-04 14:49:45 | DEBUG | 消息内容 '跳得我头疼' 不匹配任何命令，忽略
2025-08-04 14:49:47 | DEBUG | 收到消息: {'MsgId': 467836568, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290194, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_/5IoQOm+|v1_MjrtRv0V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7460100815741800063, 'MsgSeq': 871424928}
2025-08-04 14:49:47 | INFO | 收到表情消息: 消息ID:467836568 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:[捂脸]
2025-08-04 14:49:47 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7460100815741800063
2025-08-04 14:49:53 | DEBUG | 收到消息: {'MsgId': 1825102798, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_vuywamzgu2z012:\n你找个落脚点'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290200, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_nd187RiH|v1_ck/3ybeH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4178162488893477844, 'MsgSeq': 871424929}
2025-08-04 14:49:53 | INFO | 收到文本消息: 消息ID:1825102798 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 @:[] 内容:你找个落脚点
2025-08-04 14:49:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你找个落脚点' from wxid_vuywamzgu2z012 in ***********@chatroom
2025-08-04 14:49:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['你找个落脚点']
2025-08-04 14:49:53 | DEBUG | 处理消息内容: '你找个落脚点'
2025-08-04 14:49:53 | DEBUG | 消息内容 '你找个落脚点' 不匹配任何命令，忽略
2025-08-04 14:50:22 | DEBUG | 收到消息: {'MsgId': 669016973, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_vuywamzgu2z012:\n跳起来的时候点翅膀那个飞，就能浮着'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290229, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_acGSsKvG|v1_Sepb6f+k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1555752318933389055, 'MsgSeq': 871424930}
2025-08-04 14:50:22 | INFO | 收到文本消息: 消息ID:669016973 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 @:[] 内容:跳起来的时候点翅膀那个飞，就能浮着
2025-08-04 14:50:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '跳起来的时候点翅膀那个飞，就能浮着' from wxid_vuywamzgu2z012 in ***********@chatroom
2025-08-04 14:50:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['跳起来的时候点翅膀那个飞，就能浮着']
2025-08-04 14:50:22 | DEBUG | 处理消息内容: '跳起来的时候点翅膀那个飞，就能浮着'
2025-08-04 14:50:22 | DEBUG | 消息内容 '跳起来的时候点翅膀那个飞，就能浮着' 不匹配任何命令，忽略
2025-08-04 14:50:25 | DEBUG | 收到消息: {'MsgId': 1343218358, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="314db052c1847c0b51794ce3eff22482" len = "5167" productid="" androidmd5="314db052c1847c0b51794ce3eff22482" androidlen="5167" s60v3md5 = "314db052c1847c0b51794ce3eff22482" s60v3len="5167" s60v5md5 = "314db052c1847c0b51794ce3eff22482" s60v5len="5167" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=314db052c1847c0b51794ce3eff22482&amp;filekey=30340201010420301e02020106040253480410314db052c1847c0b51794ce3eff224820202142f040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b5000ee6ad950c9c370000010600004f50534828034b00b6d05aeac&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=30521179addfd3e55a1a8baeb38feff6&amp;filekey=30340201010420301e0202010604025348041030521179addfd3e55a1a8baeb38feff602021430040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b60000df53950c9c370000010600004f5053481bd34b00b6cfd4d17&amp;bizid=1023" aeskey= "1cd9b41a5bdf1d6ba4d514bdc1e24df4" externurl = "" externmd5 = "" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290231, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_K6XsCuMQ|v1_xBHZ2lkp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5491272951740218320, 'MsgSeq': 871424931}
2025-08-04 14:50:25 | INFO | 收到表情消息: 消息ID:1343218358 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 MD5:314db052c1847c0b51794ce3eff22482 大小:5167
2025-08-04 14:50:25 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5491272951740218320
2025-08-04 14:50:36 | DEBUG | 收到消息: {'MsgId': 110339656, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290243, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_M4H1Ukom|v1_nM4stNqX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 走', 'NewMsgId': 6153583831311368272, 'MsgSeq': 871424932}
2025-08-04 14:50:36 | INFO | 收到文本消息: 消息ID:110339656 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:走
2025-08-04 14:50:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '走' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:50:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['走']
2025-08-04 14:50:36 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:479d9683141301494753f07fd93dff19 总长度:9992069
2025-08-04 14:50:36 | DEBUG | 处理消息内容: '走'
2025-08-04 14:50:36 | DEBUG | 消息内容 '走' 不匹配任何命令，忽略
2025-08-04 14:50:48 | DEBUG | 收到消息: {'MsgId': 1165496127, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n睡觉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290255, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_+eBoB9oD|v1_7s/iA4Y/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 睡觉', 'NewMsgId': 6574542177233016191, 'MsgSeq': 871424935}
2025-08-04 14:50:48 | INFO | 收到文本消息: 消息ID:1165496127 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:睡觉
2025-08-04 14:50:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '睡觉' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:50:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['睡觉']
2025-08-04 14:50:48 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:455c7859ba566dc6767cb330625d52d6 总长度:9992069
2025-08-04 14:50:48 | DEBUG | 处理消息内容: '睡觉'
2025-08-04 14:50:48 | DEBUG | 消息内容 '睡觉' 不匹配任何命令，忽略
2025-08-04 14:50:55 | DEBUG | 收到消息: {'MsgId': 134104084, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n乏了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290262, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_ExhepLlX|v1_9D8NBphg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 乏了', 'NewMsgId': 1770908000266324299, 'MsgSeq': 871424938}
2025-08-04 14:50:55 | INFO | 收到文本消息: 消息ID:134104084 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:乏了
2025-08-04 14:50:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '乏了' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:50:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['乏了']
2025-08-04 14:50:55 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:109c00cad26a40805d15a2f77c15dc00 总长度:9992069
2025-08-04 14:50:55 | DEBUG | 处理消息内容: '乏了'
2025-08-04 14:50:55 | DEBUG | 消息内容 '乏了' 不匹配任何命令，忽略
2025-08-04 14:51:13 | DEBUG | 收到消息: {'MsgId': 940354997, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n摆烂'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290280, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_38YvlawB|v1_wYMGllk5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 摆烂', 'NewMsgId': 856997184804489, 'MsgSeq': 871424941}
2025-08-04 14:51:13 | INFO | 收到文本消息: 消息ID:940354997 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:摆烂
2025-08-04 14:51:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '摆烂' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:51:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['摆烂']
2025-08-04 14:51:14 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:a1b6ed5ea0186b7bfd267fd8ac419c8f 总长度:9992069
2025-08-04 14:51:14 | DEBUG | 处理消息内容: '摆烂'
2025-08-04 14:51:14 | DEBUG | 消息内容 '摆烂' 不匹配任何命令，忽略
2025-08-04 14:51:15 | DEBUG | 收到消息: {'MsgId': 45736961, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n想换头像'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290282, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Ivms7UFo|v1_8BpIuSwU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 想换头像', 'NewMsgId': 1179752702465523980, 'MsgSeq': 871424944}
2025-08-04 14:51:15 | INFO | 收到文本消息: 消息ID:45736961 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:想换头像
2025-08-04 14:51:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '想换头像' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:51:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['想换头像']
2025-08-04 14:51:15 | DEBUG | 处理消息内容: '想换头像'
2025-08-04 14:51:15 | DEBUG | 消息内容 '想换头像' 不匹配任何命令，忽略
2025-08-04 14:51:18 | DEBUG | 收到消息: {'MsgId': 1873337169, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n吃瓜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290285, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_8k5eQeOW|v1_79GmZhrO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 吃瓜', 'NewMsgId': 9047983007563562045, 'MsgSeq': 871424945}
2025-08-04 14:51:18 | INFO | 收到文本消息: 消息ID:1873337169 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:吃瓜
2025-08-04 14:51:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '吃瓜' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:51:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['吃瓜']
2025-08-04 14:51:18 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:902130814e427f28961a1a0f9b644cbe 总长度:9992069
2025-08-04 14:51:18 | DEBUG | 处理消息内容: '吃瓜'
2025-08-04 14:51:18 | DEBUG | 消息内容 '吃瓜' 不匹配任何命令，忽略
2025-08-04 14:51:19 | DEBUG | 收到消息: {'MsgId': 2007916566, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n帮我挑一个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290285, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_aBnXxbHl|v1_qTHfCCND</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 帮我挑一个', 'NewMsgId': 4159189345395966119, 'MsgSeq': 871424946}
2025-08-04 14:51:19 | INFO | 收到文本消息: 消息ID:2007916566 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:帮我挑一个
2025-08-04 14:51:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '帮我挑一个' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:51:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['帮我挑一个']
2025-08-04 14:51:19 | DEBUG | 处理消息内容: '帮我挑一个'
2025-08-04 14:51:19 | DEBUG | 消息内容 '帮我挑一个' 不匹配任何命令，忽略
2025-08-04 14:51:23 | DEBUG | 收到消息: {'MsgId': 2129906989, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290290, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_cy/fwffy|v1_4vH2b3YO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [破涕为笑]', 'NewMsgId': 4028752455343687691, 'MsgSeq': 871424949}
2025-08-04 14:51:23 | INFO | 收到表情消息: 消息ID:2129906989 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[破涕为笑]
2025-08-04 14:51:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4028752455343687691
2025-08-04 14:51:26 | DEBUG | 收到消息: {'MsgId': 1634132399, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'mei135798642:\n<msg><emoji fromusername="mei135798642" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="455c7859ba566dc6767cb330625d52d6" len="958428" productid="" androidmd5="455c7859ba566dc6767cb330625d52d6" androidlen="958428" s60v3md5="455c7859ba566dc6767cb330625d52d6" s60v3len="958428" s60v5md5="455c7859ba566dc6767cb330625d52d6" s60v5len="958428" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=455c7859ba566dc6767cb330625d52d6&amp;filekey=30440201010430302e02016e0402535a0420343535633738353962613536366463363736376362333330363235643532643602030e9fdc040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd4400007d5ee7de4992c0000006e01004fb1535a0026dbc1e685e41f4&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6c3eb07a198aa37553bec96501dc99d2&amp;filekey=30440201010430302e02016e0402535a0420366333656230376131393861613337353533626563393635303164633939643202030e9fe0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd44000096e1c7de4992c0000006e02004fb2535a0026dbc1e685e4221&amp;ef=2&amp;bizid=1022" aeskey="9c352ce584ac4e9583c3f43f720e0c92" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=964a333b05a3898e7259f68f9964a19c&amp;filekey=30440201010430302e02016e0402535a04203936346133333362303561333839386537323539663638663939363461313963020300add0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd440000ae6d67de4992c0000006e03004fb3535a0026dbc1e685e423d&amp;ef=3&amp;bizid=1022" externmd5="53f492c27fc62e0f60b39ff7b0d2677a" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290293, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_HRgIQrde|v1_iWiKN0oV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '兰雀在群聊中发了一个表情', 'NewMsgId': 1944654443050566412, 'MsgSeq': 871424950}
2025-08-04 14:51:26 | INFO | 收到表情消息: 消息ID:1634132399 来自:***********@chatroom 发送人:mei135798642 MD5:455c7859ba566dc6767cb330625d52d6 大小:958428
2025-08-04 14:51:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1944654443050566412
2025-08-04 14:51:33 | DEBUG | 收到消息: {'MsgId': 1478911045, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="522ff9e075e3a6244ee4459a302d7f4d" encryver="1" cdnthumbaeskey="522ff9e075e3a6244ee4459a302d7f4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" cdnthumblength="3832" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" length="766747" md5="b916baf74027271c850fdb21d0df6b8a" hevc_mid_size="72230" originsourcemd5="4b3e40d48373972423ff3c516119047d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjkwMTBiMDUxMDA1MTIwMTAiLCJwZHFIYXNoIjoiZTU2ZGIyODY4MjEzYWQ3ZWZj\nZTQwMjliM2VkMmQxNDk1YmExY2IzNWZjNDY0NGNhYTJiOGJmNzU0MDAzNWI4ZSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290300, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>70a05358ad00ba31d17c9e9f3560c83e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_T9J64cr9|v1_L80sZoPr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 8497115118727686216, 'MsgSeq': 871424951}
2025-08-04 14:51:33 | INFO | 收到图片消息: 消息ID:1478911045 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="522ff9e075e3a6244ee4459a302d7f4d" encryver="1" cdnthumbaeskey="522ff9e075e3a6244ee4459a302d7f4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" cdnthumblength="3832" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" length="766747" md5="b916baf74027271c850fdb21d0df6b8a" hevc_mid_size="72230" originsourcemd5="4b3e40d48373972423ff3c516119047d"><secHashInfoBase64>eyJwaGFzaCI6IjkwMTBiMDUxMDA1MTIwMTAiLCJwZHFIYXNoIjoiZTU2ZGIyODY4MjEzYWQ3ZWZjZTQwMjliM2VkMmQxNDk1YmExY2IzNWZjNDY0NGNhYTJiOGJmNzU0MDAzNWI4ZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:51:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:51:34 | INFO | [TimerTask] 缓存图片消息: 1478911045
2025-08-04 14:51:34 | DEBUG | 收到消息: {'MsgId': 1093941951, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="683240cc52c9d72419a32cc41760d494" encryver="1" cdnthumbaeskey="683240cc52c9d72419a32cc41760d494" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587c042463636133333531352d313238362d346530352d623934612d363464636532376634386364020405250a020201000405004c53da00" cdnthumblength="3053" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587c042463636133333531352d313238362d346530352d623934612d363464636532376634386364020405250a020201000405004c53da00" length="349670" md5="cf776931318ccdb3116c37a80e8f8b08" hevc_mid_size="35160" originsourcemd5="8ce60a1185ecf3ff38647a604b4809ab">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY1MTBmNWQwMTA1MDEwMTAiLCJwZHFIYXNoIjoiZTYzNmY3MWE4ZDBkMzQ2NDcw\nZDM4OGUxMzFjMWM2MWZjZmM0M2YxYjMzM2VjMDZjOWM4NzM2MTNjNzYxY2JlYyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290301, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cc2e152586ef68e91323424a9873b8df_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_AScMfhKi|v1_vDWSVijb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 7492560313890824102, 'MsgSeq': 871424952}
2025-08-04 14:51:34 | INFO | 收到图片消息: 消息ID:1093941951 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="683240cc52c9d72419a32cc41760d494" encryver="1" cdnthumbaeskey="683240cc52c9d72419a32cc41760d494" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587c042463636133333531352d313238362d346530352d623934612d363464636532376634386364020405250a020201000405004c53da00" cdnthumblength="3053" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587c042463636133333531352d313238362d346530352d623934612d363464636532376634386364020405250a020201000405004c53da00" length="349670" md5="cf776931318ccdb3116c37a80e8f8b08" hevc_mid_size="35160" originsourcemd5="8ce60a1185ecf3ff38647a604b4809ab"><secHashInfoBase64>eyJwaGFzaCI6ImY1MTBmNWQwMTA1MDEwMTAiLCJwZHFIYXNoIjoiZTYzNmY3MWE4ZDBkMzQ2NDcwZDM4OGUxMzFjMWM2MWZjZmM0M2YxYjMzM2VjMDZjOWM4NzM2MTNjNzYxY2JlYyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:51:35 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:51:35 | INFO | [TimerTask] 缓存图片消息: 1093941951
2025-08-04 14:51:35 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="0276f80b3d8d9155e62adec5a2a95d4d" encryver="1" cdnthumbaeskey="0276f80b3d8d9155e62adec5a2a95d4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587e042465346637306634662d343330372d346336302d393338362d383037326437666238343763020405290a020201000405004c54a200" cdnthumblength="3649" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587e042465346637306634662d343330372d346336302d393338362d383037326437666238343763020405290a020201000405004c54a200" length="427888" md5="56950dd90e6d23e3001d5821418850fe" hevc_mid_size="45830" originsourcemd5="cf0275c278c8a702c25b835d33f5ca56">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMTA1MDEwMTAxMDAwMTAiLCJwZHFIYXNoIjoiZTQyN2JjYzA5OTljYTMzYjI2\nYzdjZTQ3M2JjNDAzM2RiNDkzYzAxM2I5Y2MyYjZlOWU3YjlhMDExMDExZmZlZSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290302, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>f657612356322ff2919a07c8436fe8ae_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_QW8dJc5N|v1_8D5jFrZ3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 4041594043898132897, 'MsgSeq': 871424953}
2025-08-04 14:51:35 | INFO | 收到图片消息: 消息ID:********* 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="0276f80b3d8d9155e62adec5a2a95d4d" encryver="1" cdnthumbaeskey="0276f80b3d8d9155e62adec5a2a95d4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587e042465346637306634662d343330372d346336302d393338362d383037326437666238343763020405290a020201000405004c54a200" cdnthumblength="3649" cdnthumbheight="432" cdnthumbwidth="192" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587e042465346637306634662d343330372d346336302d393338362d383037326437666238343763020405290a020201000405004c54a200" length="427888" md5="56950dd90e6d23e3001d5821418850fe" hevc_mid_size="45830" originsourcemd5="cf0275c278c8a702c25b835d33f5ca56"><secHashInfoBase64>eyJwaGFzaCI6IjEwMTA1MDEwMTAxMDAwMTAiLCJwZHFIYXNoIjoiZTQyN2JjYzA5OTljYTMzYjI2YzdjZTQ3M2JjNDAzM2RiNDkzYzAxM2I5Y2MyYjZlOWU3YjlhMDExMDExZmZlZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:51:36 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:51:36 | INFO | [TimerTask] 缓存图片消息: *********
2025-08-04 14:51:37 | DEBUG | 收到消息: {'MsgId': 1039779260, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n这仨，要哪个？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290304, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_VrxTp/Ca|v1_HqhXwhBB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这仨，要哪个？', 'NewMsgId': 8038523983616235221, 'MsgSeq': 871424954}
2025-08-04 14:51:37 | INFO | 收到文本消息: 消息ID:1039779260 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:这仨，要哪个？
2025-08-04 14:51:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这仨，要哪个？' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:51:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['这仨，要哪个？']
2025-08-04 14:51:38 | DEBUG | 处理消息内容: '这仨，要哪个？'
2025-08-04 14:51:38 | DEBUG | 消息内容 '这仨，要哪个？' 不匹配任何命令，忽略
2025-08-04 14:51:50 | DEBUG | 收到消息: {'MsgId': 1388486934, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'mei135798642:\n<msg><emoji fromusername="mei135798642" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="545efeb87ebdd5aecad1941d5f08faef" len="1380327" productid="" androidmd5="545efeb87ebdd5aecad1941d5f08faef" androidlen="1380327" s60v3md5="545efeb87ebdd5aecad1941d5f08faef" s60v3len="1380327" s60v5md5="545efeb87ebdd5aecad1941d5f08faef" s60v5len="1380327" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=545efeb87ebdd5aecad1941d5f08faef&amp;filekey=30440201010430302e02016e04025348042035343565666562383765626464356165636164313934316435663038666165660203150fe7040d00000004627466730000000132&amp;hy=SH&amp;storeid=268482cc100092f0fee950a8d0000006e01004fb2534813061b01e6dc3f58e&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3864b0b8c0898fca6a5e08bc54f35247&amp;filekey=30440201010430302e02016e04025348042033383634623062386330383938666361366135653038626335346633353234370203150ff0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268482cc1000b9abeee950a8d0000006e02004fb2534813061b01e6dc3f5c6&amp;ef=2&amp;bizid=1022" aeskey="368c77b3af9e478c89851ee2bef889bc" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=e05eb7be30e9ee3682a566aa41c54b77&amp;filekey=30440201010430302e02016e0402534804206530356562376265333065396565333638326135363661613431633534623737020300e750040d00000004627466730000000132&amp;hy=SH&amp;storeid=268482cc1000e3183ee950a8d0000006e03004fb3534813061b01e6dc3f617&amp;ef=3&amp;bizid=1022" externmd5="2cac33f0c196ca2f317a98cd019cf92a" width="299" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290318, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_/6k3g3xi|v1_KPQwlS7l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '兰雀在群聊中发了一个表情', 'NewMsgId': 1465436744203542176, 'MsgSeq': 871424955}
2025-08-04 14:51:50 | INFO | 收到表情消息: 消息ID:1388486934 来自:***********@chatroom 发送人:mei135798642 MD5:545efeb87ebdd5aecad1941d5f08faef 大小:1380327
2025-08-04 14:51:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1465436744203542176
2025-08-04 14:52:19 | DEBUG | 收到消息: {'MsgId': 1435802869, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="59aa04237421c19036e0ec2e554d2adb" encryver="1" cdnthumbaeskey="59aa04237421c19036e0ec2e554d2adb" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058a9042462326138646266642d316532622d343634362d383138622d646334613065376539646364020405250a020201000405004c4d9b00" cdnthumblength="3491" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058a9042462326138646266642d316532622d343634362d383138622d646334613065376539646364020405250a020201000405004c4d9b00" length="151254" md5="ce49b3c85834345aaaf9003174f87312" hevc_mid_size="151254" originsourcemd5="2d192879a4bd9c0cef7a30358fd829d9">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMzAxMDAwMTAwMDAwMDAiLCJwZHFoYXNoIjoiOGNiMmUyNTA3ZDhlYTE2NzJmNWJmODk5OGYwNjcwZjNlZDA0N2EyYmFiY2Q1MTc4NWU4N2U5NTMxMjY0YjQ4MiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290346, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>3e515a1fc140e571cefcde20204585bd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_wcEjSyzQ|v1_xHCm/6CR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一张图片', 'NewMsgId': 2289413679723652951, 'MsgSeq': 871424956}
2025-08-04 14:52:19 | INFO | 收到图片消息: 消息ID:1435802869 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 XML:<?xml version="1.0"?><msg><img aeskey="59aa04237421c19036e0ec2e554d2adb" encryver="1" cdnthumbaeskey="59aa04237421c19036e0ec2e554d2adb" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058a9042462326138646266642d316532622d343634362d383138622d646334613065376539646364020405250a020201000405004c4d9b00" cdnthumblength="3491" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058a9042462326138646266642d316532622d343634362d383138622d646334613065376539646364020405250a020201000405004c4d9b00" length="151254" md5="ce49b3c85834345aaaf9003174f87312" hevc_mid_size="151254" originsourcemd5="2d192879a4bd9c0cef7a30358fd829d9"><secHashInfoBase64>eyJwaGFzaCI6IjUwMzAxMDAwMTAwMDAwMDAiLCJwZHFoYXNoIjoiOGNiMmUyNTA3ZDhlYTE2NzJmNWJmODk5OGYwNjcwZjNlZDA0N2EyYmFiY2Q1MTc4NWU4N2U5NTMxMjY0YjQ4MiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:52:20 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:52:20 | INFO | [TimerTask] 缓存图片消息: 1435802869
2025-08-04 14:52:22 | DEBUG | 收到消息: {'MsgId': 1105853059, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="387baf27d94a62ae6bab91cd61a0d0f0" encryver="1" cdnthumbaeskey="387baf27d94a62ae6bab91cd61a0d0f0" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058ac042465626664623737322d353637332d346464312d396131322d343430363838353237653762020405290a020201000405004c505700" cdnthumblength="4122" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058ac042465626664623737322d353637332d346464312d396131322d343430363838353237653762020405290a020201000405004c505700" length="176308" md5="d1a5bfd8fc97cd4078dacde1210beb5f" hevc_mid_size="176308" originsourcemd5="cec7c6c05d94659355d1fbb8686d6999">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMjAwMDQwMDAiLCJwZHFoYXNoIjoiOTNjMmM5MzU2NjVkMmRlNDVlMWFhODJkMDNkNGRlMGJhNWYxYzM0ZTU0YmE5YjgxNTY3ZWY5OWY4NjQwNjFkNiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290349, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>2161e46926c9e6394a07dc972183c745_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_DU6STLl9|v1_5rIQ9jv8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一张图片', 'NewMsgId': 9043543869653000120, 'MsgSeq': 871424957}
2025-08-04 14:52:22 | INFO | 收到图片消息: 消息ID:1105853059 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 XML:<?xml version="1.0"?><msg><img aeskey="387baf27d94a62ae6bab91cd61a0d0f0" encryver="1" cdnthumbaeskey="387baf27d94a62ae6bab91cd61a0d0f0" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058ac042465626664623737322d353637332d346464312d396131322d343430363838353237653762020405290a020201000405004c505700" cdnthumblength="4122" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058ac042465626664623737322d353637332d346464312d396131322d343430363838353237653762020405290a020201000405004c505700" length="176308" md5="d1a5bfd8fc97cd4078dacde1210beb5f" hevc_mid_size="176308" originsourcemd5="cec7c6c05d94659355d1fbb8686d6999"><secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMjAwMDQwMDAiLCJwZHFoYXNoIjoiOTNjMmM5MzU2NjVkMmRlNDVlMWFhODJkMDNkNGRlMGJhNWYxYzM0ZTU0YmE5YjgxNTY3ZWY5OWY4NjQwNjFkNiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:52:22 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:52:22 | INFO | [TimerTask] 缓存图片消息: 1105853059
2025-08-04 14:52:26 | DEBUG | 收到消息: {'MsgId': 2052741217, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a812b466b3a769588ac9bd7156ab65a1" encryver="1" cdnthumbaeskey="a812b466b3a769588ac9bd7156ab65a1" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058b0042430656563613638302d646364332d346361322d386266622d616635373835343635356232020405290a020201000405004c57c300" cdnthumblength="4116" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058b0042430656563613638302d646364332d346361322d386266622d616635373835343635356232020405290a020201000405004c57c300" length="173695" md5="f3e98094826f1a2a7b702611e936c591" hevc_mid_size="173695" originsourcemd5="612ba49e9536eca05a2f345d4596020d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMTAxMDAwMTAxMDAwMDAiLCJwZHFoYXNoIjoiN2FmYzhiMDFmMDc2Mzc4ZGNlMTdkOGM4NmUwZmE1NzAxZThmNmU5MGExNWM1YTJlMDNiMDM1ZDVmODBhY2FhZiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290353, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>11ee5a487ab0edc2cbbb4dc8f0278ec5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_GeLM/Y80|v1_puQaNvkU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一张图片', 'NewMsgId': 7538205665554803327, 'MsgSeq': 871424958}
2025-08-04 14:52:26 | INFO | 收到图片消息: 消息ID:2052741217 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 XML:<?xml version="1.0"?><msg><img aeskey="a812b466b3a769588ac9bd7156ab65a1" encryver="1" cdnthumbaeskey="a812b466b3a769588ac9bd7156ab65a1" cdnthumburl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058b0042430656563613638302d646364332d346361322d386266622d616635373835343635356232020405290a020201000405004c57c300" cdnthumblength="4116" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f53349af02032dcfbf02043581b9750204689058b0042430656563613638302d646364332d346361322d386266622d616635373835343635356232020405290a020201000405004c57c300" length="173695" md5="f3e98094826f1a2a7b702611e936c591" hevc_mid_size="173695" originsourcemd5="612ba49e9536eca05a2f345d4596020d"><secHashInfoBase64>eyJwaGFzaCI6IjEwMTAxMDAwMTAxMDAwMDAiLCJwZHFoYXNoIjoiN2FmYzhiMDFmMDc2Mzc4ZGNlMTdkOGM4NmUwZmE1NzAxZThmNmU5MGExNWM1YTJlMDNiMDM1ZDVmODBhY2FhZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:52:27 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:52:27 | INFO | [TimerTask] 缓存图片消息: 2052741217
2025-08-04 14:52:31 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n救命'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290358, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_pX9i/nOj|v1_OYaJulDw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5962279359070893330, 'MsgSeq': 871424959}
2025-08-04 14:52:31 | INFO | 收到文本消息: 消息ID:********* 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:救命
2025-08-04 14:52:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '救命' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:52:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['救命']
2025-08-04 14:52:31 | DEBUG | 处理消息内容: '救命'
2025-08-04 14:52:31 | DEBUG | 消息内容 '救命' 不匹配任何命令，忽略
2025-08-04 14:52:33 | DEBUG | 收到消息: {'MsgId': 1762693814, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n做运动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290360, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_GLT2vH3i|v1_FYBmT2lV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 做运动', 'NewMsgId': 7137301131151560489, 'MsgSeq': 871424960}
2025-08-04 14:52:33 | INFO | 收到文本消息: 消息ID:1762693814 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:做运动
2025-08-04 14:52:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '做运动' from wxid_ubbh6q832tcs21 in ***********@chatroom
2025-08-04 14:52:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['做运动']
2025-08-04 14:52:34 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-08-04 14:52:34 | DEBUG | 处理消息内容: '做运动'
2025-08-04 14:52:34 | DEBUG | 消息内容 '做运动' 不匹配任何命令，忽略
2025-08-04 14:52:34 | DEBUG | 收到消息: {'MsgId': 1012933524, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n就那几个台阶'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290361, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_lYq0/kNZ|v1_wG77/T/U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2610694385611947815, 'MsgSeq': 871424963}
2025-08-04 14:52:34 | INFO | 收到文本消息: 消息ID:1012933524 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:就那几个台阶
2025-08-04 14:52:34 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就那几个台阶' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:52:34 | DEBUG | [DouBaoImageToImage] 命令解析: ['就那几个台阶']
2025-08-04 14:52:34 | DEBUG | 处理消息内容: '就那几个台阶'
2025-08-04 14:52:34 | DEBUG | 消息内容 '就那几个台阶' 不匹配任何命令，忽略
2025-08-04 14:52:37 | DEBUG | 收到消息: {'MsgId': 1323713856, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n好多美女哦'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290365, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_u+SFdSNP|v1_RK4e+O5c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8621085598161108897, 'MsgSeq': 871424964}
2025-08-04 14:52:37 | INFO | 收到文本消息: 消息ID:1323713856 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:好多美女哦
2025-08-04 14:52:37 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好多美女哦' from wxid_4183511832012 in ***********@chatroom
2025-08-04 14:52:37 | DEBUG | [DouBaoImageToImage] 命令解析: ['好多美女哦']
2025-08-04 14:52:37 | DEBUG | 处理消息内容: '好多美女哦'
2025-08-04 14:52:37 | DEBUG | 消息内容 '好多美女哦' 不匹配任何命令，忽略
2025-08-04 14:52:41 | DEBUG | 收到消息: {'MsgId': 1654158760, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n死活跳死了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290368, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_alLrUAXq|v1_DwA0cFbx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5474620867944058607, 'MsgSeq': 871424965}
2025-08-04 14:52:41 | INFO | 收到文本消息: 消息ID:1654158760 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:死活跳死了
2025-08-04 14:52:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '死活跳死了' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:52:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['死活跳死了']
2025-08-04 14:52:41 | DEBUG | 处理消息内容: '死活跳死了'
2025-08-04 14:52:41 | DEBUG | 消息内容 '死活跳死了' 不匹配任何命令，忽略
2025-08-04 14:52:44 | DEBUG | 收到消息: {'MsgId': 1009700781, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\ncpdd'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290369, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_kkUtGAx6|v1_VXzSFaRH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1551060033836351679, 'MsgSeq': 871424966}
2025-08-04 14:52:44 | INFO | 收到文本消息: 消息ID:1009700781 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:cpdd
2025-08-04 14:52:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: 'cpdd' from wxid_4183511832012 in ***********@chatroom
2025-08-04 14:52:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['cpdd']
2025-08-04 14:52:44 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:f6df9912d76d07b7c0a9d2f2dd054553 总长度:9992069
2025-08-04 14:52:44 | DEBUG | 处理消息内容: 'cpdd'
2025-08-04 14:52:44 | DEBUG | 消息内容 'cpdd' 不匹配任何命令，忽略
2025-08-04 14:53:03 | DEBUG | 收到消息: {'MsgId': 1112845631, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n@璟⃪訫⃪°\u2005来拽我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290390, 'MsgSource': '<msgsource>\n\t<atuserlist>wangchunmeng7291</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_42ZISed1|v1_cbwI61GY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6040124259543085624, 'MsgSeq': 871424969}
2025-08-04 14:53:03 | INFO | 收到文本消息: 消息ID:1112845631 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:['wangchunmeng7291'] 内容:@璟⃪訫⃪° 来拽我
2025-08-04 14:53:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@璟⃪訫⃪° 来拽我' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:53:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['@璟⃪訫⃪°\u2005来拽我']
2025-08-04 14:53:03 | DEBUG | 处理消息内容: '@璟⃪訫⃪° 来拽我'
2025-08-04 14:53:03 | DEBUG | 消息内容 '@璟⃪訫⃪° 来拽我' 不匹配任何命令，忽略
2025-08-04 14:53:07 | DEBUG | 收到消息: {'MsgId': 511537805, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n[机智]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290394, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_pYNuJH+X|v1_gNqWlSnX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2510326237384108637, 'MsgSeq': 871424970}
2025-08-04 14:53:07 | INFO | 收到表情消息: 消息ID:511537805 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:[机智]
2025-08-04 14:53:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2510326237384108637
2025-08-04 14:53:25 | DEBUG | 收到消息: {'MsgId': 1263841084, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>或者你上我号给我整了算了[害羞][害羞]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3432610669527387983</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wangchunmeng7291</chatusr>\n\t\t\t<displayname>璟⃪訫⃪°</displayname>\n\t\t\t<content>@知鱼ˇ\u2005有那么大难度，我那天蹦跶几下就上去了[捂脸]</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;853126771&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_8kgajq8ks1nv22&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;150&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_7vXKf/zV|v1_5HE4NWS8&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754290127</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_8kgajq8ks1nv22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290412, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>cfb134074502898e141c4d37668dcb58_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_2hh14cUx|v1_IjUT9qJr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2205612096597394498, 'MsgSeq': 871424971}
2025-08-04 14:53:25 | DEBUG | 从群聊消息中提取发送者: wxid_8kgajq8ks1nv22
2025-08-04 14:53:25 | DEBUG | 使用已解析的XML处理引用消息
2025-08-04 14:53:25 | INFO | 收到引用消息: 消息ID:1263841084 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 内容:或者你上我号给我整了算了[害羞][害羞] 引用类型:1
2025-08-04 14:53:25 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-04 14:53:25 | INFO | [DouBaoImageToImage] 消息内容: '或者你上我号给我整了算了[害羞][害羞]' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:53:25 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['或者你上我号给我整了算了[害羞][害羞]']
2025-08-04 14:53:25 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-04 14:53:25 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-04 14:53:25 | INFO |   - 消息内容: 或者你上我号给我整了算了[害羞][害羞]
2025-08-04 14:53:25 | INFO |   - 群组ID: ***********@chatroom
2025-08-04 14:53:25 | INFO |   - 发送人: wxid_8kgajq8ks1nv22
2025-08-04 14:53:25 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@知鱼ˇ\u2005有那么大难度，我那天蹦跶几下就上去了[捂脸]', 'Msgid': '3432610669527387983', 'NewMsgId': '3432610669527387983', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '璟⃪訫⃪°', 'MsgSource': '<msgsource><sequence_id>853126771</sequence_id>\n\t<atuserlist>wxid_8kgajq8ks1nv22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_7vXKf/zV|v1_5HE4NWS8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754290127', 'SenderWxid': 'wxid_8kgajq8ks1nv22'}
2025-08-04 14:53:25 | INFO |   - 引用消息ID: 
2025-08-04 14:53:25 | INFO |   - 引用消息类型: 
2025-08-04 14:53:25 | INFO |   - 引用消息内容: @知鱼ˇ 有那么大难度，我那天蹦跶几下就上去了[捂脸]
2025-08-04 14:53:25 | INFO |   - 引用消息发送人: wxid_8kgajq8ks1nv22
2025-08-04 14:53:39 | DEBUG | 收到消息: {'MsgId': 1420345336, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n你还在那吗，@知鱼ˇ\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290426, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_8kgajq8ks1nv22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_MzkjLaeW|v1_2JlFbWJR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8128307364715411522, 'MsgSeq': 871424972}
2025-08-04 14:53:39 | INFO | 收到文本消息: 消息ID:1420345336 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:['wxid_8kgajq8ks1nv22'] 内容:你还在那吗，@知鱼ˇ 
2025-08-04 14:53:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你还在那吗，@知鱼ˇ' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 14:53:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['你还在那吗，@知鱼ˇ']
2025-08-04 14:53:39 | DEBUG | 处理消息内容: '你还在那吗，@知鱼ˇ'
2025-08-04 14:53:39 | DEBUG | 消息内容 '你还在那吗，@知鱼ˇ' 不匹配任何命令，忽略
2025-08-04 14:53:42 | DEBUG | 收到消息: {'MsgId': 1373125756, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n我还有好几个宝箱没拿到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290429, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_H6EY7K3r|v1_FN8igPnI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3184251959235371376, 'MsgSeq': 871424973}
2025-08-04 14:53:42 | INFO | 收到文本消息: 消息ID:1373125756 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:我还有好几个宝箱没拿到
2025-08-04 14:53:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我还有好几个宝箱没拿到' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:53:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['我还有好几个宝箱没拿到']
2025-08-04 14:53:42 | DEBUG | 处理消息内容: '我还有好几个宝箱没拿到'
2025-08-04 14:53:42 | DEBUG | 消息内容 '我还有好几个宝箱没拿到' 不匹配任何命令，忽略
2025-08-04 14:53:44 | DEBUG | 收到消息: {'MsgId': 85921883, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n我晚点了行不'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290430, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_wJ7MN3Xo|v1_THOSvsal</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3952543728315354368, 'MsgSeq': 871424974}
2025-08-04 14:53:44 | INFO | 收到文本消息: 消息ID:85921883 来自:***********@chatroom 发送人:wangchunmeng7291 @:[] 内容:我晚点了行不
2025-08-04 14:53:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我晚点了行不' from wangchunmeng7291 in ***********@chatroom
2025-08-04 14:53:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['我晚点了行不']
2025-08-04 14:53:44 | DEBUG | 处理消息内容: '我晚点了行不'
2025-08-04 14:53:44 | DEBUG | 消息内容 '我晚点了行不' 不匹配任何命令，忽略
2025-08-04 14:53:47 | DEBUG | 收到消息: {'MsgId': 1469516718, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n我在'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290431, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_J2cVfeXK|v1_DS0VTesX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4051634261682464255, 'MsgSeq': 871424975}
2025-08-04 14:53:47 | INFO | 收到文本消息: 消息ID:1469516718 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:我在
2025-08-04 14:53:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我在' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:53:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['我在']
2025-08-04 14:53:47 | DEBUG | 处理消息内容: '我在'
2025-08-04 14:53:47 | DEBUG | 消息内容 '我在' 不匹配任何命令，忽略
2025-08-04 14:53:49 | DEBUG | 收到消息: {'MsgId': 1157445861, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n我掉下来了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290435, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_YBCOE5Rq|v1_iWPfWImU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7559310464307598467, 'MsgSeq': 871424976}
2025-08-04 14:53:49 | INFO | 收到文本消息: 消息ID:1157445861 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:我掉下来了
2025-08-04 14:53:49 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我掉下来了' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:53:49 | DEBUG | [DouBaoImageToImage] 命令解析: ['我掉下来了']
2025-08-04 14:53:49 | DEBUG | 处理消息内容: '我掉下来了'
2025-08-04 14:53:49 | DEBUG | 消息内容 '我掉下来了' 不匹配任何命令，忽略
2025-08-04 14:53:51 | DEBUG | 收到消息: {'MsgId': 176243590, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290438, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_o9rda5Hc|v1_3+2Oechz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4679809808525478527, 'MsgSeq': 871424977}
2025-08-04 14:53:51 | INFO | 收到表情消息: 消息ID:176243590 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:[捂脸]
2025-08-04 14:53:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4679809808525478527
2025-08-04 14:54:03 | DEBUG | 收到消息: {'MsgId': 1664139553, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n不在蛋糕那里了？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290450, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_qhG/Z86j|v1_t821TnKQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5792986028743450224, 'MsgSeq': 871424978}
2025-08-04 14:54:03 | INFO | 收到文本消息: 消息ID:1664139553 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:不在蛋糕那里了？
2025-08-04 14:54:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不在蛋糕那里了？' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 14:54:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['不在蛋糕那里了？']
2025-08-04 14:54:03 | DEBUG | 处理消息内容: '不在蛋糕那里了？'
2025-08-04 14:54:03 | DEBUG | 消息内容 '不在蛋糕那里了？' 不匹配任何命令，忽略
2025-08-04 14:54:05 | DEBUG | 收到消息: {'MsgId': 2069745064, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n我整一上午，眼睛都不行了快'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290450, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_H34DjRIs|v1_6JM4Hkzv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2253523075416216179, 'MsgSeq': 871424979}
2025-08-04 14:54:05 | INFO | 收到文本消息: 消息ID:2069745064 来自:***********@chatroom 发送人:wangchunmeng7291 @:[] 内容:我整一上午，眼睛都不行了快
2025-08-04 14:54:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我整一上午，眼睛都不行了快' from wangchunmeng7291 in ***********@chatroom
2025-08-04 14:54:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['我整一上午，眼睛都不行了快']
2025-08-04 14:54:05 | DEBUG | 处理消息内容: '我整一上午，眼睛都不行了快'
2025-08-04 14:54:05 | DEBUG | 消息内容 '我整一上午，眼睛都不行了快' 不匹配任何命令，忽略
2025-08-04 14:54:23 | DEBUG | 收到消息: {'MsgId': 1659988264, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290470, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_DUlrqvdC|v1_uDS44pJL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6435236743166614488, 'MsgSeq': 871424980}
2025-08-04 14:54:23 | INFO | 收到文本消息: 消息ID:1659988264 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]
2025-08-04 14:54:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 14:54:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]']
2025-08-04 14:54:23 | DEBUG | 处理消息内容: '我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]'
2025-08-04 14:54:23 | DEBUG | 消息内容 '我还指望你拉我上蛋糕，我再登顶拉你呢[憨笑]' 不匹配任何命令，忽略
2025-08-04 14:54:28 | DEBUG | 收到消息: {'MsgId': 1579057839, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6b6f6795648db0b00f0f509e69343880" encryver="1" cdnthumbaeskey="6b6f6795648db0b00f0f509e69343880" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02043361962402046890592b042462326635373561642d343238612d346565642d383065352d653435323233306538376265020405250a020201000405004c51e700" cdnthumblength="4550" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02043361962402046890592b042462326635373561642d343238612d346565642d383065352d653435323233306538376265020405250a020201000405004c51e700" length="185391" md5="6f56c46bd2b2a1b60b6561904cc97163" hevc_mid_size="185391" originsourcemd5="52af12904685d6a761db1387d5b91bec">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc1OTAwMTEwNTAwMDEwMTAiLCJwZHFoYXNoIjoiNTRhYmFjNTY1NmM4NjhkNjI1OGMxNjM2MmFhZDBkMjY4OGU5OTlmYWE0ZmEzNGQ4MWZmMjM2YmNhODMzZWI1YiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n\t<extcommoninfo>\n\t\t<refermsg>\n\t\t\t<svrid>3952543728315354368</svrid>\n\t\t\t<signature>N0_V1_zppgS9rF|v1_oHWhga5m</signature>\n\t\t\t<createtime>1754290430</createtime>\n\t\t</refermsg>\n\t</extcommoninfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290475, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>941a5b0024757660df7a6d358f4a569e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_dpTxoJzA|v1_JcLRduEH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7843977328257854972, 'MsgSeq': 871424981}
2025-08-04 14:54:28 | INFO | 收到图片消息: 消息ID:1579057839 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 XML:<?xml version="1.0"?><msg><img aeskey="6b6f6795648db0b00f0f509e69343880" encryver="1" cdnthumbaeskey="6b6f6795648db0b00f0f509e69343880" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b02043361962402046890592b042462326635373561642d343238612d346565642d383065352d653435323233306538376265020405250a020201000405004c51e700" cdnthumblength="4550" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b02043361962402046890592b042462326635373561642d343238612d346565642d383065352d653435323233306538376265020405250a020201000405004c51e700" length="185391" md5="6f56c46bd2b2a1b60b6561904cc97163" hevc_mid_size="185391" originsourcemd5="52af12904685d6a761db1387d5b91bec"><secHashInfoBase64>eyJwaGFzaCI6Ijc1OTAwMTEwNTAwMDEwMTAiLCJwZHFoYXNoIjoiNTRhYmFjNTY1NmM4NjhkNjI1OGMxNjM2MmFhZDBkMjY4OGU5OTlmYWE0ZmEzNGQ4MWZmMjM2YmNhODMzZWI1YiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo><extcommoninfo><refermsg><svrid>3952543728315354368</svrid><signature>N0_V1_zppgS9rF|v1_oHWhga5m</signature><createtime>1754290430</createtime></refermsg></extcommoninfo></msg>
2025-08-04 14:54:28 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:54:28 | INFO | [TimerTask] 缓存图片消息: 1579057839
2025-08-04 14:54:32 | DEBUG | 收到消息: {'MsgId': 1501903013, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n这不掉下来了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290480, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_s820nzKp|v1_AGjLhs7M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8705339845570871563, 'MsgSeq': 871424982}
2025-08-04 14:54:32 | INFO | 收到文本消息: 消息ID:1501903013 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:这不掉下来了
2025-08-04 14:54:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这不掉下来了' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 14:54:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['这不掉下来了']
2025-08-04 14:54:32 | DEBUG | 处理消息内容: '这不掉下来了'
2025-08-04 14:54:32 | DEBUG | 消息内容 '这不掉下来了' 不匹配任何命令，忽略
2025-08-04 14:54:36 | DEBUG | 收到消息: {'MsgId': 1991436874, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290484, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_naeL6zjL|v1_kSp3HCi2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4616901908783650755, 'MsgSeq': 871424983}
2025-08-04 14:54:36 | INFO | 收到表情消息: 消息ID:1991436874 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:[破涕为笑]
2025-08-04 14:54:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4616901908783650755
2025-08-04 14:54:43 | DEBUG | 收到消息: {'MsgId': 496344656, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n那你等我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290491, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_Ub7zs/GW|v1_sYd/AgrX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 958303318978034958, 'MsgSeq': 871424984}
2025-08-04 14:54:43 | INFO | 收到文本消息: 消息ID:496344656 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:那你等我
2025-08-04 14:54:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那你等我' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 14:54:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['那你等我']
2025-08-04 14:54:43 | DEBUG | 处理消息内容: '那你等我'
2025-08-04 14:54:43 | DEBUG | 消息内容 '那你等我' 不匹配任何命令，忽略
2025-08-04 14:56:15 | DEBUG | 收到消息: {'MsgId': 35923349, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_002lrj9uidgz22:\n红果不是免费可以看吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290582, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_CQG0KeXV|v1_JYsVjQBR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7 : 红果不是免费可以看吗', 'NewMsgId': 2211889101576875619, 'MsgSeq': 871424985}
2025-08-04 14:56:15 | INFO | 收到文本消息: 消息ID:35923349 来自:***********@chatroom 发送人:wxid_002lrj9uidgz22 @:[] 内容:红果不是免费可以看吗
2025-08-04 14:56:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '红果不是免费可以看吗' from wxid_002lrj9uidgz22 in ***********@chatroom
2025-08-04 14:56:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['红果不是免费可以看吗']
2025-08-04 14:56:15 | DEBUG | 处理消息内容: '红果不是免费可以看吗'
2025-08-04 14:56:15 | DEBUG | 消息内容 '红果不是免费可以看吗' 不匹配任何命令，忽略
2025-08-04 14:56:22 | DEBUG | 收到消息: {'MsgId': 738690656, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="55bc60db9d3146afa168c82a8c05c693" encryver="1" cdnthumbaeskey="55bc60db9d3146afa168c82a8c05c693" cdnthumburl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" cdnthumblength="3812" cdnthumbheight="92" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" length="110576" cdnbigimgurl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" hdlength="597348" md5="fc2bb41a1ebb59fd2e4ab99edca0221e" hevc_mid_size="110576">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290589, 'MsgSource': '<msgsource>\n\t<img_file_name>b7770105-e862-4bdd-92f3-579d246ddc9e.png</img_file_name>\n\t<alnode>\n\t\t<fr>1</fr>\n\t\t<cf>3</cf>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ffc9456702337474be2de2d3be67cc46_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_QIeEGrwy|v1_LGpjJdIp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Garson在群聊中发了一张图片', 'NewMsgId': 4666004971818056438, 'MsgSeq': 871424986}
2025-08-04 14:56:22 | INFO | 收到图片消息: 消息ID:738690656 来自:***********@chatroom 发送人:wxid_9uwska6u4yzm22 XML:<?xml version="1.0"?><msg><img aeskey="55bc60db9d3146afa168c82a8c05c693" encryver="1" cdnthumbaeskey="55bc60db9d3146afa168c82a8c05c693" cdnthumburl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" cdnthumblength="3812" cdnthumbheight="92" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" length="110576" cdnbigimgurl="************4b30490201000204fa6599a102032f84110204adba587d02046890599c042437626539303461312d653537322d343632622d626663332d383964346436666437666336020405252a010201000405004c53d900" hdlength="597348" md5="fc2bb41a1ebb59fd2e4ab99edca0221e" hevc_mid_size="110576"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 14:56:22 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 14:56:22 | INFO | [TimerTask] 缓存图片消息: 738690656
2025-08-04 14:56:48 | DEBUG | 收到消息: {'MsgId': 1283576378, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n可以玩很久了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290616, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_d+VxqP65|v1_lbUPmA1U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Garson : 可以玩很久了', 'NewMsgId': 3263376093904684460, 'MsgSeq': 871424987}
2025-08-04 14:56:48 | INFO | 收到文本消息: 消息ID:1283576378 来自:***********@chatroom 发送人:wxid_9uwska6u4yzm22 @:[] 内容:可以玩很久了
2025-08-04 14:56:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '可以玩很久了' from wxid_9uwska6u4yzm22 in ***********@chatroom
2025-08-04 14:56:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['可以玩很久了']
2025-08-04 14:56:48 | DEBUG | 处理消息内容: '可以玩很久了'
2025-08-04 14:56:48 | DEBUG | 消息内容 '可以玩很久了' 不匹配任何命令，忽略
2025-08-04 14:56:55 | DEBUG | 收到消息: {'MsgId': 1099231207, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n<msg><emoji fromusername = "wxid_9uwska6u4yzm22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="87c19fdf39abe33e55aa987833f78796" len = "9931" productid="com.tencent.xin.emoticon.person.stiker_1617177076959bf2c048405087" androidmd5="87c19fdf39abe33e55aa987833f78796" androidlen="9931" s60v3md5 = "87c19fdf39abe33e55aa987833f78796" s60v3len="9931" s60v5md5 = "87c19fdf39abe33e55aa987833f78796" s60v5len="9931" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=87c19fdf39abe33e55aa987833f78796&amp;filekey=30340201010420301e0202011304025348041087c19fdf39abe33e55aa987833f78796020226cb040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479d851000d2caf000000000000011300004f50534825ed2a00b723febda&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=3239764f386d030b8e0f0c1708c71993&amp;filekey=30340201010420301e020201130402534804103239764f386d030b8e0f0c1708c7199302023c27040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479d851000ec607000000000000011300004f505348058028e0b66063578&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=12c916be46367f2ce2ff929f1be0ba62&amp;filekey=30340201010420301e0202010604025348041012c916be46367f2ce2ff929f1be0ba62020226d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26316882a000bf84b000000000000010600004f50534828865b40b77b964e4&amp;bizid=1023" aeskey= "6bd26a8ba83f4197639627cb120cd313" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=c94886fd52230e9124e7fdfa68751217&amp;filekey=30340201010420301e02020106040253480410c94886fd52230e9124e7fdfa6875121702020be0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26316882a000e224c000000000000010600004f50534808465b40b77ac1260&amp;bizid=1023" externmd5 = "e577d375d9fb2321debf57f9a858cd95" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgblkJDoiIwQlvq1gwY=" linkid= "" desc= "Cg8KBXpoX2NuEgblkJDoiIwKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290622, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_ZNMOGflI|v1_y3VsMaPK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Garson在群聊中发了一个表情', 'NewMsgId': 4196112084371090757, 'MsgSeq': 871424988}
2025-08-04 14:56:55 | INFO | 收到表情消息: 消息ID:1099231207 来自:***********@chatroom 发送人:wxid_9uwska6u4yzm22 MD5:87c19fdf39abe33e55aa987833f78796 大小:9931
2025-08-04 14:56:55 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4196112084371090757
2025-08-04 14:57:12 | DEBUG | 收到消息: {'MsgId': 2083052914, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_002lrj9uidgz22:\n番茄小说也可以免费看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290639, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_CHVRGZI3|v1_SBkbrV3F</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7 : 番茄小说也可以免费看', 'NewMsgId': 5957482446235607823, 'MsgSeq': 871424989}
2025-08-04 14:57:12 | INFO | 收到文本消息: 消息ID:2083052914 来自:***********@chatroom 发送人:wxid_002lrj9uidgz22 @:[] 内容:番茄小说也可以免费看
2025-08-04 14:57:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '番茄小说也可以免费看' from wxid_002lrj9uidgz22 in ***********@chatroom
2025-08-04 14:57:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['番茄小说也可以免费看']
2025-08-04 14:57:12 | DEBUG | 处理消息内容: '番茄小说也可以免费看'
2025-08-04 14:57:12 | DEBUG | 消息内容 '番茄小说也可以免费看' 不匹配任何命令，忽略
2025-08-04 14:58:03 | DEBUG | 收到消息: {'MsgId': 199034317, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="183" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290691, 'MsgSource': '<msgsource>\n\t<signature>v1_CdGoBrIV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6460371434793958975, 'MsgSeq': 871424990}
2025-08-04 14:58:16 | DEBUG | 收到消息: {'MsgId': 1935496732, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_002lrj9uidgz22:\n@锦岚\u2005第一个可以'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290703, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Cnp+AkLY|v1_S4eP4c5o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7 : @锦岚\u2005第一个可以', 'NewMsgId': 4497914611621479704, 'MsgSeq': 871424991}
2025-08-04 14:58:16 | INFO | 收到文本消息: 消息ID:1935496732 来自:***********@chatroom 发送人:wxid_002lrj9uidgz22 @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 第一个可以
2025-08-04 14:58:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@锦岚 第一个可以' from wxid_002lrj9uidgz22 in ***********@chatroom
2025-08-04 14:58:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['@锦岚\u2005第一个可以']
2025-08-04 14:58:16 | DEBUG | 处理消息内容: '@锦岚 第一个可以'
2025-08-04 14:58:16 | DEBUG | 消息内容 '@锦岚 第一个可以' 不匹配任何命令，忽略
2025-08-04 14:58:19 | DEBUG | 收到消息: {'MsgId': 365216315, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="184" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290704, 'MsgSource': '<msgsource>\n\t<signature>v1_SMyf/985</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 423962892532051995, 'MsgSeq': 871424992}
2025-08-04 14:58:22 | DEBUG | 收到消息: {'MsgId': 1934294189, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n[苦涩]那两个找不到地方下载'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290709, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_NcsKIDT9|v1_Wx70RuR7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : [苦涩]那两个找不到地方下载', 'NewMsgId': 2615657319747104327, 'MsgSeq': 871424993}
2025-08-04 14:58:22 | INFO | 收到文本消息: 消息ID:1934294189 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:[苦涩]那两个找不到地方下载
2025-08-04 14:58:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[苦涩]那两个找不到地方下载' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:58:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['[苦涩]那两个找不到地方下载']
2025-08-04 14:58:22 | DEBUG | 处理消息内容: '[苦涩]那两个找不到地方下载'
2025-08-04 14:58:22 | DEBUG | 消息内容 '[苦涩]那两个找不到地方下载' 不匹配任何命令，忽略
2025-08-04 14:58:28 | DEBUG | 收到消息: {'MsgId': 2137176664, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>8497115118727686216</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;70a05358ad00ba31d17c9e9f3560c83e_&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;img aeskey="522ff9e075e3a6244ee4459a302d7f4d" encryver="1" cdnthumbaeskey="522ff9e075e3a6244ee4459a302d7f4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" cdnthumblength="3832" cdnthumbheight="432" cdnthumbwidth="192" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" length="766747" md5="b916baf74027271c850fdb21d0df6b8a" hevc_mid_size="72230" originsourcemd5="4b3e40d48373972423ff3c516119047d"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjkwMTBiMDUxMDA1MTIwMTAiLCJwZHFIYXNoIjoiZTU2ZGIyODY4MjEzYWQ3ZWZj&amp;#x0A;ZTQwMjliM2VkMmQxNDk1YmExY2IzNWZjNDY0NGNhYTJiOGJmNzU0MDAzNWI4ZSJ9&amp;#x0A;&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;/msg&gt;</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754290298</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290715, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>70a05358ad00ba31d17c9e9f3560c83e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_m8E6SsYm|v1_9nsdsP4b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这个？', 'NewMsgId': 8083123827037117222, 'MsgSeq': 871424994}
2025-08-04 14:58:28 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-04 14:58:28 | DEBUG | 使用已解析的XML处理引用消息
2025-08-04 14:58:28 | INFO | 收到引用消息: 消息ID:2137176664 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:这个？ 引用类型:3
2025-08-04 14:58:29 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-04 14:58:29 | INFO | [DouBaoImageToImage] 消息内容: '这个？' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:58:29 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['这个？']
2025-08-04 14:58:29 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-04 14:58:29 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-04 14:58:29 | INFO |   - 消息内容: 这个？
2025-08-04 14:58:29 | INFO |   - 群组ID: ***********@chatroom
2025-08-04 14:58:29 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-08-04 14:58:29 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>8497115118727686216</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;70a05358ad00ba31d17c9e9f3560c83e_&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;img aeskey="522ff9e075e3a6244ee4459a302d7f4d" encryver="1" cdnthumbaeskey="522ff9e075e3a6244ee4459a302d7f4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" cdnthumblength="3832" cdnthumbheight="432" cdnthumbwidth="192" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" length="766747" md5="b916baf74027271c850fdb21d0df6b8a" hevc_mid_size="72230" originsourcemd5="4b3e40d48373972423ff3c516119047d"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjkwMTBiMDUxMDA1MTIwMTAiLCJwZHFIYXNoIjoiZTU2ZGIyODY4MjEzYWQ3ZWZj&amp;#x0A;ZTQwMjliM2VkMmQxNDk1YmExY2IzNWZjNDY0NGNhYTJiOGJmNzU0MDAzNWI4ZSJ9&amp;#x0A;&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;/msg&gt;</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754290298</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '8497115118727686216', 'NewMsgId': '8497115118727686216', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '锦岚', 'MsgSource': '<msgsource><sec_msg_node><uuid>70a05358ad00ba31d17c9e9f3560c83e_</uuid></sec_msg_node></msgsource>', 'Createtime': '1754290298', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-08-04 14:58:29 | INFO |   - 引用消息ID: 
2025-08-04 14:58:29 | INFO |   - 引用消息类型: 
2025-08-04 14:58:29 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>这个？</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>8497115118727686216</svrid>
			<fromusr>***********@chatroom</fromusr>
			<chatusr>wxid_wlnzvr8ivgd422</chatusr>
			<displayname>锦岚</displayname>
			<msgsource>&lt;msgsource&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;70a05358ad00ba31d17c9e9f3560c83e_&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;/msgsource&gt;</msgsource>
			<content>&lt;msg&gt;&lt;img aeskey="522ff9e075e3a6244ee4459a302d7f4d" encryver="1" cdnthumbaeskey="522ff9e075e3a6244ee4459a302d7f4d" cdnthumburl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" cdnthumblength="3832" cdnthumbheight="432" cdnthumbwidth="192" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204677ac2dc02046890587b042435613837363861352d363262322d343938302d613336342d663462643638616639613764020405290a020201000405004c537600" length="766747" md5="b916baf74027271c850fdb21d0df6b8a" hevc_mid_size="72230" originsourcemd5="4b3e40d48373972423ff3c516119047d"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjkwMTBiMDUxMDA1MTIwMTAiLCJwZHFIYXNoIjoiZTU2ZGIyODY4MjEzYWQ3ZWZj&amp;#x0A;ZTQwMjliM2VkMmQxNDk1YmExY2IzNWZjNDY0NGNhYTJiOGJmNzU0MDAzNWI4ZSJ9&amp;#x0A;&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;/msg&gt;</content>
			<strid />
			<createtime>1754290298</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-04 14:58:29 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-08-04 14:58:46 | DEBUG | 收到消息: {'MsgId': 1605490567, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_002lrj9uidgz22:\n对'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290733, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Mib1yR2k|v1_mi+PwKbS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '7 : 对', 'NewMsgId': 8938931900547461948, 'MsgSeq': 871424995}
2025-08-04 14:58:46 | INFO | 收到文本消息: 消息ID:1605490567 来自:***********@chatroom 发送人:wxid_002lrj9uidgz22 @:[] 内容:对
2025-08-04 14:58:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '对' from wxid_002lrj9uidgz22 in ***********@chatroom
2025-08-04 14:58:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['对']
2025-08-04 14:58:46 | DEBUG | 处理消息内容: '对'
2025-08-04 14:58:46 | DEBUG | 消息内容 '对' 不匹配任何命令，忽略
2025-08-04 14:58:54 | DEBUG | 收到消息: {'MsgId': 1311331543, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\nokk'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290741, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_uQn43mXP|v1_0AO84WbW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : okk', 'NewMsgId': 6596445895085615057, 'MsgSeq': 871424996}
2025-08-04 14:58:54 | INFO | 收到文本消息: 消息ID:1311331543 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:okk
2025-08-04 14:58:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: 'okk' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 14:58:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['okk']
2025-08-04 14:58:55 | DEBUG | 处理消息内容: 'okk'
2025-08-04 14:58:55 | DEBUG | 消息内容 'okk' 不匹配任何命令，忽略
2025-08-04 14:59:22 | DEBUG | 收到消息: {'MsgId': 1818364752, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'mei135798642:\n<msg><emoji fromusername="mei135798642" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="d9dfe9447e23cc239622fb20826c54bb" len="2226779" productid="" androidmd5="d9dfe9447e23cc239622fb20826c54bb" androidlen="2226779" s60v3md5="d9dfe9447e23cc239622fb20826c54bb" s60v3len="2226779" s60v5md5="d9dfe9447e23cc239622fb20826c54bb" s60v5len="2226779" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=d9dfe9447e23cc239622fb20826c54bb&amp;filekey=30440201010430302e02016e0402535a04206439646665393434376532336363323339363232666232303832366335346262020321fa5b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26825c4f100031d13183cc91a0000006e01004fb1535a016d915157aed8b05&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4e029bcecc3770a2c85cbd53e595bb29&amp;filekey=30440201010430302e02016e0402535a04203465303239626365636333373730613263383563626435336535393562623239020321fa60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26825c4f100058c97183cc91a0000006e02004fb2535a016d915157aed8b2f&amp;ef=2&amp;bizid=1022" aeskey="813518b037b845c0b67d7d380002a6d9" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a84f2320c3cac0cba36d62361b32d57b&amp;filekey=30440201010430302e02016e0402535a042061383466323332306333636163306362613336643632333631623332643537620203041ad0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26825c4f1000817cb183cc91a0000006e03004fb3535a016d915157aed8b4f&amp;ef=3&amp;bizid=1022" externmd5="34d350226bb992f9ad08e4a0c87503ac" width="512" height="512" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290769, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_3uQquf6T|v1_Rzw5tQRo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '兰雀在群聊中发了一个表情', 'NewMsgId': 1537853226575210364, 'MsgSeq': 871424997}
2025-08-04 14:59:22 | INFO | 收到表情消息: 消息ID:1818364752 来自:***********@chatroom 发送人:mei135798642 MD5:d9dfe9447e23cc239622fb20826c54bb 大小:2226779
2025-08-04 14:59:22 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1537853226575210364
2025-08-04 15:02:07 | DEBUG | 收到消息: {'MsgId': 1411572606, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="1df06eec2e2c2cfc09a4aad3b6551806" encryver="1" cdnthumbaeskey="1df06eec2e2c2cfc09a4aad3b6551806" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905af6042464346166363762302d656561322d343761322d383537632d613739633534336363316230020405250a020201000405004c4d9b00" cdnthumblength="4636" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905af6042464346166363762302d656561322d343761322d383537632d613739633534336363316230020405250a020201000405004c4d9b00" length="200773" md5="78af24b75abd440f638aac54f3cbfe91" hevc_mid_size="200773" originsourcemd5="199170c377002c6df0de0c87089189a2">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY0NzA1MzQ5MDAwMDkwMDAiLCJwZHFoYXNoIjoiYzQyY2EzZjQ0YmQ4ZDRjMjJmOWM1MGIyYWJjZDQ4ZWJiNmI0YjgzMzZmMzNiNDBlNWFmMjQzYmNhODUzMTZjMyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290934, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>255b5d0caaf11ba2fd0235bde8b8e2e6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_DIkBRYrC|v1_FiPMS+rq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5872525471303986352, 'MsgSeq': 871424998}
2025-08-04 15:02:07 | INFO | 收到图片消息: 消息ID:1411572606 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 XML:<?xml version="1.0"?><msg><img aeskey="1df06eec2e2c2cfc09a4aad3b6551806" encryver="1" cdnthumbaeskey="1df06eec2e2c2cfc09a4aad3b6551806" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905af6042464346166363762302d656561322d343761322d383537632d613739633534336363316230020405250a020201000405004c4d9b00" cdnthumblength="4636" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905af6042464346166363762302d656561322d343761322d383537632d613739633534336363316230020405250a020201000405004c4d9b00" length="200773" md5="78af24b75abd440f638aac54f3cbfe91" hevc_mid_size="200773" originsourcemd5="199170c377002c6df0de0c87089189a2"><secHashInfoBase64>eyJwaGFzaCI6ImY0NzA1MzQ5MDAwMDkwMDAiLCJwZHFoYXNoIjoiYzQyY2EzZjQ0YmQ4ZDRjMjJmOWM1MGIyYWJjZDQ4ZWJiNmI0YjgzMzZmMzNiNDBlNWFmMjQzYmNhODUzMTZjMyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:02:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:02:07 | INFO | [TimerTask] 缓存图片消息: 1411572606
2025-08-04 15:02:10 | DEBUG | 收到消息: {'MsgId': 1194708573, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n@枂菟ིྀ\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290938, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_h2GHVA5i|v1_M879X+DY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6237589417086983940, 'MsgSeq': 871424999}
2025-08-04 15:02:10 | INFO | 收到文本消息: 消息ID:1194708573 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:['wxid_5kipwrzramxr22'] 内容:@枂菟ིྀ 
2025-08-04 15:02:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@枂菟ིྀ' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 15:02:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['@枂菟ིྀ']
2025-08-04 15:02:10 | DEBUG | 处理消息内容: '@枂菟ིྀ'
2025-08-04 15:02:10 | DEBUG | 消息内容 '@枂菟ིྀ' 不匹配任何命令，忽略
2025-08-04 15:02:32 | DEBUG | 收到消息: {'MsgId': 373227368, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3ezwcnjm7pvz22:\n<msg><emoji fromusername = "wxid_3ezwcnjm7pvz22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="dc162a89d70e891519c81826865ebe41" len = "717203" productid="" androidmd5="dc162a89d70e891519c81826865ebe41" androidlen="717203" s60v3md5 = "dc162a89d70e891519c81826865ebe41" s60v3len="717203" s60v5md5 = "dc162a89d70e891519c81826865ebe41" s60v5len="717203" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc162a89d70e891519c81826865ebe41&amp;filekey=30350201010421301f02020106040253480410dc162a89d70e891519c81826865ebe4102030af193040d00000004627466730000000132&amp;hy=SH&amp;storeid=268025a1e00083b512be522190000010600004f5053480e934b00b6d4ed2fe&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=a8f8628cc0ed8e49634eef9edf00879e&amp;filekey=30350201010421301f02020106040253480410a8f8628cc0ed8e49634eef9edf00879e02030af1a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268025a1e000ba4572be522190000010600004f50534816fa41b156f122a3e&amp;bizid=1023" aeskey= "b52e5e6ef1cef34ea94a3706c86e93d9" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=e212e430aac75ac4675ba683fa33eedd&amp;filekey=30350201010421301f020201060402535a0410e212e430aac75ac4675ba683fa33eedd020300ebf0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268025a4e00054df0961166fc0000010600004f50535a1befb011578e9d764&amp;bizid=1023" externmd5 = "ffd517eb118bddd739c4664da4a1b206" width= "396" height= "396" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290959, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_jbzXh4/h|v1_FNTpWmGg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '天特🥇肿瘤电场仪厂家在群聊中发了一个表情', 'NewMsgId': 6332664210657489668, 'MsgSeq': 871425000}
2025-08-04 15:02:32 | INFO | 收到表情消息: 消息ID:373227368 来自:***********@chatroom 发送人:wxid_3ezwcnjm7pvz22 MD5:dc162a89d70e891519c81826865ebe41 大小:717203
2025-08-04 15:02:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6332664210657489668
2025-08-04 15:02:38 | DEBUG | 收到消息: {'MsgId': 368865498, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@知鱼ˇ\u2005咋了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290965, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_8kgajq8ks1nv22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_0fHVCmEZ|v1_0mjsgFXH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7224052853251285531, 'MsgSeq': 871425001}
2025-08-04 15:02:38 | INFO | 收到文本消息: 消息ID:368865498 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:['wxid_8kgajq8ks1nv22'] 内容:@知鱼ˇ 咋了
2025-08-04 15:02:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@知鱼ˇ 咋了' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 15:02:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['@知鱼ˇ\u2005咋了']
2025-08-04 15:02:38 | DEBUG | 处理消息内容: '@知鱼ˇ 咋了'
2025-08-04 15:02:38 | DEBUG | 消息内容 '@知鱼ˇ 咋了' 不匹配任何命令，忽略
2025-08-04 15:03:07 | DEBUG | 收到消息: {'MsgId': 1400945670, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="17fb233f9d5d77b37ee21aaa628aa169" encryver="1" cdnthumbaeskey="17fb233f9d5d77b37ee21aaa628aa169" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905b31042437303165383930612d656566642d343534332d383264392d343266643931373761666433020405290a020201000405004c57c300" cdnthumblength="4540" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905b31042437303165383930612d656566642d343534332d383264392d343266643931373761666433020405290a020201000405004c57c300" length="237999" md5="2a03066e69b81ec3dfc4fd08e528ed06" hevc_mid_size="237999" originsourcemd5="2a116f8fb439295258fb98870db640e1">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjM0YjAxMjQwNjAyMDkwOTAiLCJwZHFoYXNoIjoiZjQ2OGE3ZDY1MjZjNjlkMjBmMWViOGY0MGEyZDQ1Yjg2OGExYWQzMzM3NzNkYzBiMGJmYzVjZTNhMjUzMWNjYyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290994, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>9c56a81ff86a3ebd5f4a34bb09d5e8f4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_e61Vlsrx|v1_4uQj21I8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1224090861404014320, 'MsgSeq': 871425002}
2025-08-04 15:03:07 | INFO | 收到图片消息: 消息ID:1400945670 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 XML:<?xml version="1.0"?><msg><img aeskey="17fb233f9d5d77b37ee21aaa628aa169" encryver="1" cdnthumbaeskey="17fb233f9d5d77b37ee21aaa628aa169" cdnthumburl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905b31042437303165383930612d656566642d343534332d383264392d343266643931373761666433020405290a020201000405004c57c300" cdnthumblength="4540" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204cbf35d4502032e1d7b020433619624020468905b31042437303165383930612d656566642d343534332d383264392d343266643931373761666433020405290a020201000405004c57c300" length="237999" md5="2a03066e69b81ec3dfc4fd08e528ed06" hevc_mid_size="237999" originsourcemd5="2a116f8fb439295258fb98870db640e1"><secHashInfoBase64>eyJwaGFzaCI6IjM0YjAxMjQwNjAyMDkwOTAiLCJwZHFoYXNoIjoiZjQ2OGE3ZDY1MjZjNjlkMjBmMWViOGY0MGEyZDQ1Yjg2OGExYWQzMzM3NzNkYzBiMGJmYzVjZTNhMjUzMWNjYyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:03:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:03:07 | INFO | [TimerTask] 缓存图片消息: 1400945670
2025-08-04 15:03:12 | DEBUG | 收到消息: {'MsgId': 1840966646, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n一个硬'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754290999, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_a7mochb5|v1_WmB7hAT1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 836881101208739549, 'MsgSeq': 871425003}
2025-08-04 15:03:12 | INFO | 收到文本消息: 消息ID:1840966646 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:一个硬
2025-08-04 15:03:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '一个硬' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 15:03:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['一个硬']
2025-08-04 15:03:12 | DEBUG | 处理消息内容: '一个硬'
2025-08-04 15:03:12 | DEBUG | 消息内容 '一个硬' 不匹配任何命令，忽略
2025-08-04 15:03:19 | DEBUG | 收到消息: {'MsgId': 900180785, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n银'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291006, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_f26/G8Sp|v1_TRDl315M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8639180604473130915, 'MsgSeq': 871425004}
2025-08-04 15:03:19 | INFO | 收到文本消息: 消息ID:900180785 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:银
2025-08-04 15:03:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '银' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 15:03:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['银']
2025-08-04 15:03:19 | DEBUG | 处理消息内容: '银'
2025-08-04 15:03:19 | DEBUG | 消息内容 '银' 不匹配任何命令，忽略
2025-08-04 15:03:21 | DEBUG | 收到消息: {'MsgId': 264869809, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n666'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291007, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_MZWnxvEF|v1_kxtUpkQc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 666', 'NewMsgId': 5777863361068903037, 'MsgSeq': 871425005}
2025-08-04 15:03:21 | INFO | 收到文本消息: 消息ID:264869809 来自:***********@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:666
2025-08-04 15:03:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '666' from wxid_62fiham2pn7521 in ***********@chatroom
2025-08-04 15:03:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['666']
2025-08-04 15:03:21 | DEBUG | 处理消息内容: '666'
2025-08-04 15:03:21 | DEBUG | 消息内容 '666' 不匹配任何命令，忽略
2025-08-04 15:03:24 | DEBUG | 收到消息: {'MsgId': 804336413, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\n一个金'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291011, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_mKt/lP6h|v1_yFTaJ9Jt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8744304289716304742, 'MsgSeq': 871425006}
2025-08-04 15:03:24 | INFO | 收到文本消息: 消息ID:804336413 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:一个金
2025-08-04 15:03:24 | DEBUG | [DouBaoImageToImage] 收到文本消息: '一个金' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 15:03:24 | DEBUG | [DouBaoImageToImage] 命令解析: ['一个金']
2025-08-04 15:03:24 | DEBUG | 处理消息内容: '一个金'
2025-08-04 15:03:24 | DEBUG | 消息内容 '一个金' 不匹配任何命令，忽略
2025-08-04 15:03:30 | DEBUG | 收到消息: {'MsgId': 1759389511, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="299e71cf6881308cd9303254b33b4eab" encryver="1" cdnthumbaeskey="299e71cf6881308cd9303254b33b4eab" cdnthumburl="************4b30490201000204e4ef8a5f02032f4f560204d07ac2dc020468905b34042433393532633236382d643637652d343739612d623130632d373166616137303439666662020405150a020201000405004c4e6200" cdnthumblength="4108" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204e4ef8a5f02032f4f560204d07ac2dc020468905b34042433393532633236382d643637652d343739612d623130632d373166616137303439666662020405150a020201000405004c4e6200" length="940014" md5="d3e725d036c036ca6fc8ffa38e3eb081" hevc_mid_size="90623" originsourcemd5="fb9ded9fca1edc5fe7b04be874fdfa3d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc1MDAzMTIwMDAxMDEwMDAiLCJwZHFIYXNoIjoiYjM0ZjczYTY0OWE2ODFjMmY3\nOTM0ZDk5OGMxYzY3MTk1NjE4YjY0M2JhNmNmODYzNzhmMGY4MTc0MTNhMDc5ZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291017, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>9e3da6e5c6f009116c3142f12990b03a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_VrB01YCj|v1_uv8YFQ/3</signature>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 544162195725043815, 'MsgSeq': 871425007}
2025-08-04 15:03:30 | INFO | 收到图片消息: 消息ID:1759389511 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="299e71cf6881308cd9303254b33b4eab" encryver="1" cdnthumbaeskey="299e71cf6881308cd9303254b33b4eab" cdnthumburl="************4b30490201000204e4ef8a5f02032f4f560204d07ac2dc020468905b34042433393532633236382d643637652d343739612d623130632d373166616137303439666662020405150a020201000405004c4e6200" cdnthumblength="4108" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204e4ef8a5f02032f4f560204d07ac2dc020468905b34042433393532633236382d643637652d343739612d623130632d373166616137303439666662020405150a020201000405004c4e6200" length="940014" md5="d3e725d036c036ca6fc8ffa38e3eb081" hevc_mid_size="90623" originsourcemd5="fb9ded9fca1edc5fe7b04be874fdfa3d"><secHashInfoBase64>eyJwaGFzaCI6Ijc1MDAzMTIwMDAxMDEwMDAiLCJwZHFIYXNoIjoiYjM0ZjczYTY0OWE2ODFjMmY3OTM0ZDk5OGMxYzY3MTk1NjE4YjY0M2JhNmNmODYzNzhmMGY4MTc0MTNhMDc5ZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:03:31 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:03:31 | INFO | [TimerTask] 缓存图片消息: 1759389511
2025-08-04 15:03:32 | DEBUG | 收到消息: {'MsgId': 1445968707, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="796265647a6e747775757263706c6665" encryver="0" cdnthumbaeskey="796265647a6e747775757263706c6665" cdnthumburl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" cdnthumblength="3443" cdnthumbheight="100" cdnthumbwidth="75" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" length="67750" cdnbigimgurl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" hdlength="185610" md5="cec4689bb3280c045fa3370419652763">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291018, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>223bab2cba34731e9d672c3709981cf7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Ij8LwB9Z|v1_WHMyYxGH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 5250287275964583588, 'MsgSeq': 871425008}
2025-08-04 15:03:32 | INFO | 收到图片消息: 消息ID:1445968707 来自:***********@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="796265647a6e747775757263706c6665" encryver="0" cdnthumbaeskey="796265647a6e747775757263706c6665" cdnthumburl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" cdnthumblength="3443" cdnthumbheight="100" cdnthumbwidth="75" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" length="67750" cdnbigimgurl="************4b30490201000204d6e1bab602033d14ba02045604b1a3020468905b4a042435353137303765362d666366332d343461352d623033382d3435333139333932643966620204052828010201000405004c50560054381738" hdlength="185610" md5="cec4689bb3280c045fa3370419652763"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:03:32 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:03:32 | INFO | [TimerTask] 缓存图片消息: 1445968707
2025-08-04 15:03:34 | DEBUG | 收到消息: {'MsgId': 97508244, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'poison3-1:\n好的谢谢大佬'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291022, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_OziDUEzN|v1_ew9r/0/G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '6400 : 好的谢谢大佬', 'NewMsgId': 1386203066581313857, 'MsgSeq': 871425009}
2025-08-04 15:03:34 | INFO | 收到文本消息: 消息ID:97508244 来自:***********@chatroom 发送人:poison3-1 @:[] 内容:好的谢谢大佬
2025-08-04 15:03:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好的谢谢大佬' from poison3-1 in ***********@chatroom
2025-08-04 15:03:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['好的谢谢大佬']
2025-08-04 15:03:35 | DEBUG | 处理消息内容: '好的谢谢大佬'
2025-08-04 15:03:35 | DEBUG | 消息内容 '好的谢谢大佬' 不匹配任何命令，忽略
2025-08-04 15:03:39 | DEBUG | 收到消息: {'MsgId': 205162498, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n那你等会，我也得返回拿那些'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291026, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_QOdM1dqq|v1_ufdsQQVm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6450750242309336382, 'MsgSeq': 871425010}
2025-08-04 15:03:39 | INFO | 收到文本消息: 消息ID:205162498 来自:***********@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:那你等会，我也得返回拿那些
2025-08-04 15:03:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那你等会，我也得返回拿那些' from wxid_5kipwrzramxr22 in ***********@chatroom
2025-08-04 15:03:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['那你等会，我也得返回拿那些']
2025-08-04 15:03:39 | DEBUG | 处理消息内容: '那你等会，我也得返回拿那些'
2025-08-04 15:03:39 | DEBUG | 消息内容 '那你等会，我也得返回拿那些' 不匹配任何命令，忽略
2025-08-04 15:03:48 | DEBUG | 收到消息: {'MsgId': 531753051, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n换了手机壳'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291036, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_4uxiZehl|v1_nCOWi7QX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 换了手机壳', 'NewMsgId': 7475751226340174476, 'MsgSeq': 871425011}
2025-08-04 15:03:48 | INFO | 收到文本消息: 消息ID:531753051 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:换了手机壳
2025-08-04 15:03:49 | DEBUG | [DouBaoImageToImage] 收到文本消息: '换了手机壳' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:03:49 | DEBUG | [DouBaoImageToImage] 命令解析: ['换了手机壳']
2025-08-04 15:03:49 | DEBUG | 处理消息内容: '换了手机壳'
2025-08-04 15:03:49 | DEBUG | 消息内容 '换了手机壳' 不匹配任何命令，忽略
2025-08-04 15:03:59 | DEBUG | 收到消息: {'MsgId': 1453407515, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8kgajq8ks1nv22:\nok '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291046, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>150</membercount>\n\t<signature>N0_V1_OoFb4MDg|v1_D5vUeop7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7937744892912533917, 'MsgSeq': 871425012}
2025-08-04 15:03:59 | INFO | 收到文本消息: 消息ID:1453407515 来自:***********@chatroom 发送人:wxid_8kgajq8ks1nv22 @:[] 内容:ok 
2025-08-04 15:03:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: 'ok' from wxid_8kgajq8ks1nv22 in ***********@chatroom
2025-08-04 15:03:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['ok']
2025-08-04 15:03:59 | DEBUG | 处理消息内容: 'ok'
2025-08-04 15:03:59 | DEBUG | 消息内容 'ok' 不匹配任何命令，忽略
2025-08-04 15:04:25 | DEBUG | 收到消息: {'MsgId': 1585810322, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'poison3-1:\n这手机壳好丑啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291072, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Nf00s7VL|v1_hvs9bHfF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '6400 : 这手机壳好丑啊', 'NewMsgId': 5271774901256136861, 'MsgSeq': 871425013}
2025-08-04 15:04:25 | INFO | 收到文本消息: 消息ID:1585810322 来自:***********@chatroom 发送人:poison3-1 @:[] 内容:这手机壳好丑啊
2025-08-04 15:04:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这手机壳好丑啊' from poison3-1 in ***********@chatroom
2025-08-04 15:04:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['这手机壳好丑啊']
2025-08-04 15:04:25 | DEBUG | 处理消息内容: '这手机壳好丑啊'
2025-08-04 15:04:25 | DEBUG | 消息内容 '这手机壳好丑啊' 不匹配任何命令，忽略
2025-08-04 15:04:28 | DEBUG | 收到消息: {'MsgId': 449897670, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'poison3-1:\n[破涕为笑][破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291074, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_nkdMYDEc|v1_FcH7GR0D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '6400 : [破涕为笑][破涕为笑]', 'NewMsgId': 4134537182279813061, 'MsgSeq': 871425014}
2025-08-04 15:04:28 | INFO | 收到表情消息: 消息ID:449897670 来自:***********@chatroom 发送人:poison3-1 @:[] 内容:[破涕为笑][破涕为笑]
2025-08-04 15:04:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4134537182279813061
2025-08-04 15:04:34 | DEBUG | 收到消息: {'MsgId': 423812718, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n[旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291081, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_DAXLb6p1|v1_Tu6zz1z5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : [旺柴]', 'NewMsgId': 7282035272960172283, 'MsgSeq': 871425015}
2025-08-04 15:04:34 | INFO | 收到表情消息: 消息ID:423812718 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:[旺柴]
2025-08-04 15:04:34 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7282035272960172283
2025-08-04 15:06:18 | DEBUG | 收到消息: {'MsgId': 2016286639, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_p0noqs6y93lp22:\n确实'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291185, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_qowQhNiX|v1_e/ikjuL2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小星 : 确实', 'NewMsgId': 4022667873196278454, 'MsgSeq': 871425016}
2025-08-04 15:06:18 | INFO | 收到文本消息: 消息ID:2016286639 来自:***********@chatroom 发送人:wxid_p0noqs6y93lp22 @:[] 内容:确实
2025-08-04 15:06:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '确实' from wxid_p0noqs6y93lp22 in ***********@chatroom
2025-08-04 15:06:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['确实']
2025-08-04 15:06:18 | DEBUG | 处理消息内容: '确实'
2025-08-04 15:06:18 | DEBUG | 消息内容 '确实' 不匹配任何命令，忽略
2025-08-04 15:07:34 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-04 15:08:04 | DEBUG | 收到消息: {'MsgId': 2067708982, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n算了，看在这壳便宜的份上'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291291, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_0mPrFpCt|v1_LNoV/d2C</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 算了，看在这壳便宜的份上', 'NewMsgId': 5001125904755420421, 'MsgSeq': 871425017}
2025-08-04 15:08:04 | INFO | 收到文本消息: 消息ID:2067708982 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:算了，看在这壳便宜的份上
2025-08-04 15:08:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '算了，看在这壳便宜的份上' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:08:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['算了，看在这壳便宜的份上']
2025-08-04 15:08:05 | DEBUG | 处理消息内容: '算了，看在这壳便宜的份上'
2025-08-04 15:08:05 | DEBUG | 消息内容 '算了，看在这壳便宜的份上' 不匹配任何命令，忽略
2025-08-04 15:08:08 | DEBUG | 收到消息: {'MsgId': 1780762185, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n原谅你们'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291295, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_DfdEHFf1|v1_4ujcThyU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 原谅你们', 'NewMsgId': 4615683292630111531, 'MsgSeq': 871425018}
2025-08-04 15:08:08 | INFO | 收到文本消息: 消息ID:1780762185 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:原谅你们
2025-08-04 15:08:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '原谅你们' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:08:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['原谅你们']
2025-08-04 15:08:08 | DEBUG | 处理消息内容: '原谅你们'
2025-08-04 15:08:08 | DEBUG | 消息内容 '原谅你们' 不匹配任何命令，忽略
2025-08-04 15:08:29 | DEBUG | 收到消息: {'MsgId': 2092834789, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d924c7b2d48fc8d90ed9aa808be9f59b" encryver="1" cdnthumbaeskey="d924c7b2d48fc8d90ed9aa808be9f59b" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c74042431323865393266302d373863612d346432332d396362652d333666313733393666333764020405250a020201000405004c53da00" cdnthumblength="5748" cdnthumbheight="432" cdnthumbwidth="288" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c74042431323865393266302d373863612d346432332d396362652d333666313733393666333764020405250a020201000405004c53da00" length="578818" md5="1327e5f8771782ac74e21b1b106404b6" hevc_mid_size="89384" originsourcemd5="0162646211ef5bc49ec04539ba69d17e">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcyMDAwMDMwMDAyMDEwMjAiLCJwZHFIYXNoIjoiMWJmMTBmZjAwNzgzZjQ5ZTBm\nZjhiZGZhZjAyYzQzODc3ODNjYzAwNzgwODc4Mjg3MDM4N2ZmN2M4N2MzOGZlOCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291316, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>6b8da5dfd61961ae46e7e2680cdf74b1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_m3iGHMxF|v1_auVOmXiR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 527801398604333639, 'MsgSeq': 871425019}
2025-08-04 15:08:29 | INFO | 收到图片消息: 消息ID:2092834789 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="d924c7b2d48fc8d90ed9aa808be9f59b" encryver="1" cdnthumbaeskey="d924c7b2d48fc8d90ed9aa808be9f59b" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c74042431323865393266302d373863612d346432332d396362652d333666313733393666333764020405250a020201000405004c53da00" cdnthumblength="5748" cdnthumbheight="432" cdnthumbwidth="288" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c74042431323865393266302d373863612d346432332d396362652d333666313733393666333764020405250a020201000405004c53da00" length="578818" md5="1327e5f8771782ac74e21b1b106404b6" hevc_mid_size="89384" originsourcemd5="0162646211ef5bc49ec04539ba69d17e"><secHashInfoBase64>eyJwaGFzaCI6IjcyMDAwMDMwMDAyMDEwMjAiLCJwZHFIYXNoIjoiMWJmMTBmZjAwNzgzZjQ5ZTBmZjhiZGZhZjAyYzQzODc3ODNjYzAwNzgwODc4Mjg3MDM4N2ZmN2M4N2MzOGZlOCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:08:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:08:30 | INFO | [TimerTask] 缓存图片消息: 2092834789
2025-08-04 15:08:33 | DEBUG | 收到消息: {'MsgId': 928803149, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n这是之前的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291320, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Y+3pIXjY|v1_BjK7U9nc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这是之前的', 'NewMsgId': 1035160485943990988, 'MsgSeq': 871425020}
2025-08-04 15:08:33 | INFO | 收到文本消息: 消息ID:928803149 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:这是之前的
2025-08-04 15:08:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这是之前的' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:08:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['这是之前的']
2025-08-04 15:08:33 | DEBUG | 处理消息内容: '这是之前的'
2025-08-04 15:08:33 | DEBUG | 消息内容 '这是之前的' 不匹配任何命令，忽略
2025-08-04 15:08:47 | DEBUG | 收到消息: {'MsgId': 1794980265, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9614255226c9396aabf994d92fe0f38b" encryver="1" cdnthumbaeskey="9614255226c9396aabf994d92fe0f38b" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c84042438366362613032662d363331612d346333332d393636392d643638643836383032363865020405250a020201000405004c53da00" cdnthumblength="5568" cdnthumbheight="432" cdnthumbwidth="292" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c84042438366362613032662d363331612d346333332d393636392d643638643836383032363865020405250a020201000405004c53da00" length="596538" md5="facff582da4d03ec48dae90cab938e2e" hevc_mid_size="93795" originsourcemd5="f044de2d99d0cd33ca0b1735ef05ac58">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMjIwMDAwMDAiLCJwZHFIYXNoIjoiNDYxOGY4ZTIwMzAzZTc4YTFm\nZjAxY2RhNTczYzAyODNlMGM3Yzc4NzNiN2M3MDdjODM4N2ViNjRlN2MyM2Y3YyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291334, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e99a6f9910e9c24505c1ed839f40430d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_y3v/022r|v1_D3p32PIK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 1594526329519521691, 'MsgSeq': 871425021}
2025-08-04 15:08:47 | INFO | 收到图片消息: 消息ID:1794980265 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="9614255226c9396aabf994d92fe0f38b" encryver="1" cdnthumbaeskey="9614255226c9396aabf994d92fe0f38b" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c84042438366362613032662d363331612d346333332d393636392d643638643836383032363865020405250a020201000405004c53da00" cdnthumblength="5568" cdnthumbheight="432" cdnthumbwidth="292" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c84042438366362613032662d363331612d346333332d393636392d643638643836383032363865020405250a020201000405004c53da00" length="596538" md5="facff582da4d03ec48dae90cab938e2e" hevc_mid_size="93795" originsourcemd5="f044de2d99d0cd33ca0b1735ef05ac58"><secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMjIwMDAwMDAiLCJwZHFIYXNoIjoiNDYxOGY4ZTIwMzAzZTc4YTFmZjAxY2RhNTczYzAyODNlMGM3Yzc4NzNiN2M3MDdjODM4N2ViNjRlN2MyM2Y3YyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:08:47 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:08:47 | INFO | [TimerTask] 缓存图片消息: 1794980265
2025-08-04 15:08:48 | DEBUG | 收到消息: {'MsgId': 1816705127, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n这是现在的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291335, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_1wDYa+Sz|v1_qxRN44/p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这是现在的', 'NewMsgId': 6417225448627468525, 'MsgSeq': 871425022}
2025-08-04 15:08:48 | INFO | 收到文本消息: 消息ID:1816705127 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:这是现在的
2025-08-04 15:08:49 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这是现在的' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:08:49 | DEBUG | [DouBaoImageToImage] 命令解析: ['这是现在的']
2025-08-04 15:08:49 | DEBUG | 处理消息内容: '这是现在的'
2025-08-04 15:08:49 | DEBUG | 消息内容 '这是现在的' 不匹配任何命令，忽略
2025-08-04 15:09:05 | DEBUG | 收到消息: {'MsgId': 923033377, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="8fb45a89b794cd08e6f5266053d5b127" encryver="1" cdnthumbaeskey="8fb45a89b794cd08e6f5266053d5b127" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c97042435663762316436612d616564322d343934652d613434662d623464336335343634646364020405250a020201000405004c4d9a00" cdnthumblength="5926" cdnthumbheight="307" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c97042435663762316436612d616564322d343934652d613434662d623464336335343634646364020405250a020201000405004c4d9a00" length="319614" md5="db5b45bf81a0b9152e90266b5ba8cf7d" hevc_mid_size="52162" originsourcemd5="ca7947cd68dfa653b0d1ca50f9f907a7">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc3MzAzMDAwMjAwMDAwMDAiLCJwZHFIYXNoIjoiMDUwNWMyMDE1ZjQzMWZmOWMx\nNTg4MGE3M2RmYTNjZjgzODdhMjEwNzdmMGZmZjVjYzc4M2MxZTdjMDg3M2UzYyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291351, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>b6a5228228f1b28b704f9854aef13e8a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_+20FTxTj|v1_yLWMk/i3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 6961969877874387000, 'MsgSeq': 871425023}
2025-08-04 15:09:05 | INFO | 收到图片消息: 消息ID:923033377 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="8fb45a89b794cd08e6f5266053d5b127" encryver="1" cdnthumbaeskey="8fb45a89b794cd08e6f5266053d5b127" cdnthumburl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c97042435663762316436612d616564322d343934652d613434662d623464336335343634646364020405250a020201000405004c4d9a00" cdnthumblength="5926" cdnthumbheight="307" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b3049020100020468cde53a02032f4f560204177bc2dc020468905c97042435663762316436612d616564322d343934652d613434662d623464336335343634646364020405250a020201000405004c4d9a00" length="319614" md5="db5b45bf81a0b9152e90266b5ba8cf7d" hevc_mid_size="52162" originsourcemd5="ca7947cd68dfa653b0d1ca50f9f907a7"><secHashInfoBase64>eyJwaGFzaCI6Ijc3MzAzMDAwMjAwMDAwMDAiLCJwZHFIYXNoIjoiMDUwNWMyMDE1ZjQzMWZmOWMxNTg4MGE3M2RmYTNjZjgzODdhMjEwNzdmMGZmZjVjYzc4M2MxZTdjMDg3M2UzYyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-04 15:09:05 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-08-04 15:09:05 | INFO | [TimerTask] 缓存图片消息: 923033377
2025-08-04 15:09:09 | DEBUG | 收到消息: {'MsgId': 1421008519, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n这是排队中的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291357, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_v+cvFQPU|v1_s9f2PHKX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这是排队中的', 'NewMsgId': 490141920476154889, 'MsgSeq': 871425024}
2025-08-04 15:09:09 | INFO | 收到文本消息: 消息ID:1421008519 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:这是排队中的
2025-08-04 15:09:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这是排队中的' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:09:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['这是排队中的']
2025-08-04 15:09:10 | DEBUG | 处理消息内容: '这是排队中的'
2025-08-04 15:09:10 | DEBUG | 消息内容 '这是排队中的' 不匹配任何命令，忽略
2025-08-04 15:09:30 | DEBUG | 收到消息: {'MsgId': 837731419, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n粉色红色绿色，用完了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291378, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_2By6yG00|v1_NvflEq8p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 粉色红色绿色，用完了', 'NewMsgId': 5165472362975353428, 'MsgSeq': 871425025}
2025-08-04 15:09:30 | INFO | 收到文本消息: 消息ID:837731419 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:粉色红色绿色，用完了
2025-08-04 15:09:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '粉色红色绿色，用完了' from wxid_wlnzvr8ivgd422 in ***********@chatroom
2025-08-04 15:09:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['粉色红色绿色，用完了']
2025-08-04 15:09:31 | DEBUG | 处理消息内容: '粉色红色绿色，用完了'
2025-08-04 15:09:31 | DEBUG | 消息内容 '粉色红色绿色，用完了' 不匹配任何命令，忽略
2025-08-04 15:09:33 | DEBUG | 收到消息: {'MsgId': 1578129984, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c90iqp1oemkj22:\n[发呆]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291380, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_egBkz5t8|v1_xt6HKe94</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '情绪 : [发呆]', 'NewMsgId': 4214402169497004920, 'MsgSeq': 871425026}
2025-08-04 15:09:33 | INFO | 收到表情消息: 消息ID:1578129984 来自:***********@chatroom 发送人:wxid_c90iqp1oemkj22 @:[] 内容:[发呆]
2025-08-04 15:09:34 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4214402169497004920
2025-08-04 15:10:16 | DEBUG | 收到消息: {'MsgId': 540669111, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'poison3-1:\n[破涕为笑][破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291423, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_pNX6aooe|v1_M3mepHEj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '6400 : [破涕为笑][破涕为笑]', 'NewMsgId': 1482020706843820401, 'MsgSeq': 871425027}
2025-08-04 15:10:16 | INFO | 收到表情消息: 消息ID:540669111 来自:***********@chatroom 发送人:poison3-1 @:[] 内容:[破涕为笑][破涕为笑]
2025-08-04 15:10:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1482020706843820401
2025-08-04 15:10:36 | DEBUG | 收到消息: {'MsgId': 1322853671, 'FromUserName': {'string': '45798398714@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wogoxis9jdfq22:\n哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291443, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>5</membercount>\n\t<signature>N0_V1_zgWIJLgj|v1_JDljkbMQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7089474530091874012, 'MsgSeq': 871425028}
2025-08-04 15:10:36 | INFO | 收到文本消息: 消息ID:1322853671 来自:45798398714@chatroom 发送人:wxid_wogoxis9jdfq22 @:[] 内容:哈哈哈哈哈
2025-08-04 15:10:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈' from wxid_wogoxis9jdfq22 in 45798398714@chatroom
2025-08-04 15:10:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈']
2025-08-04 15:10:36 | DEBUG | 处理消息内容: '哈哈哈哈哈'
2025-08-04 15:10:36 | DEBUG | 消息内容 '哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-04 15:11:30 | DEBUG | 收到消息: {'MsgId': 781125705, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="913c8799a96ac67659912c6e00557eff" len="159498" productid="" androidmd5="913c8799a96ac67659912c6e00557eff" androidlen="159498" s60v3md5="913c8799a96ac67659912c6e00557eff" s60v3len="159498" s60v5md5="913c8799a96ac67659912c6e00557eff" s60v5len="159498" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=913c8799a96ac67659912c6e00557eff&amp;filekey=30440201010430302e02016e04025348042039313363383739396139366163363736353939313263366530303535376566660203026f0a040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303061633639663864666463633166363736356234306230303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a49a33ea265ba31642351d705e25c4c8&amp;filekey=30440201010430302e02016e04025348042061343961333365613236356261333136343233353164373035653235633463380203026f10040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303062336563613864666463633166363736356234306230303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="587c9c937fc04175af84fa88b3582253" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=2c62482504be2ed17f2e5b63008f4965&amp;filekey=3043020101042f302d02016e0402534804203263363234383235303462653265643137663265356236333030386634393635020268f0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313037303430383135313830303063333137383864666463633166363736356234306230303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="4d360d62b49e0288e996db5907d83448" width="480" height="544" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754291498, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_llg4RHhC|v1_MtXkg3M4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 2737178981996050868, 'MsgSeq': 871425029}
2025-08-04 15:11:30 | INFO | 收到表情消息: 消息ID:781125705 来自:***********@chatroom 发送人:last--exile MD5:913c8799a96ac67659912c6e00557eff 大小:159498
2025-08-04 15:11:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2737178981996050868
